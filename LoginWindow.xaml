<Window x:Class="vocabulary.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="医学词汇编辑系统 - 登录" Height="550" Width="650"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        WindowStyle="None" AllowsTransparency="True">
    <Window.Resources>
        <!-- 头部样式 -->
        <Style x:Key="HeaderStyle" TargetType="Border">
            <Setter Property="Height" Value="20"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,0"/>
        </Style>
        
        <Style x:Key="SubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,30"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,0"/>
            <Setter Property="BorderBrush" Value="#e1e5e9"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="PasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,0"/>
            <Setter Property="BorderBrush" Value="#e1e5e9"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="LoginButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#66b1ff"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <TranslateTransform Y="-2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="3,2" EndPoint="0,2">
                <GradientStop Color="#409eff" Offset="0"/>
                <GradientStop Color="White" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>
        
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 自定义头部 -->
        <Border Grid.Row="0" Style="{StaticResource HeaderStyle}" MouseLeftButtonDown="HeaderBorder_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 窗口标题 -->
                <TextBlock Grid.Column="0" Text="登录" 
                          FontSize="14" FontWeight="Medium" 
                          Foreground="#333333" 
                          VerticalAlignment="Center" 
                          Margin="10,0,0,0"/>
                
                <!-- 关闭按钮 -->
                <Button Grid.Column="1" x:Name="btnClose" 
                       Width="40" Height="40" 
                       Background="Transparent" 
                       BorderThickness="0"
                       Click="BtnClose_Click"
                       Cursor="Hand" Margin="0,-10,0,-10">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}">
                                <TextBlock x:Name="textBlock" Text="✕" FontSize="16" 
                                          Foreground="#666666" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#ff4757"/>
                                    <Setter TargetName="textBlock" Property="Foreground" Value="White"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </Grid>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Border Width="400" Height="450" 
                    Background="White" 
                    CornerRadius="15"
                    HorizontalAlignment="Center" 
                    VerticalAlignment="Center">
                <Border.Effect>
                    <DropShadowEffect BlurRadius="20" 
                                      ShadowDepth="0" 
                                      Color="#22000000"/>
                </Border.Effect>
                
                <StackPanel Margin="40">
                   
                    <TextBlock Text="医学词汇编辑系统" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="请输入您的账号密码登录系统" Style="{StaticResource SubtitleStyle}"/>
                    
                    <Label Content="账号" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtUsername" Style="{StaticResource TextBoxStyle}"/>
                    
                    <Label Content="密码" Style="{StaticResource LabelStyle}"/>
                    <PasswordBox x:Name="txtPassword" Style="{StaticResource PasswordBoxStyle}"/>
                    
                    <Button x:Name="btnLogin" Content="登录系统" 
                            Style="{StaticResource LoginButtonStyle}"
                            Click="BtnLogin_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window> 