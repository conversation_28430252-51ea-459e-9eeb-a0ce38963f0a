using System;
using System.Threading.Tasks;
using System.Windows;
using vocabulary.Models;
using vocabulary.Services;

namespace vocabulary
{
    public partial class VocabularyEditWindow : Window
    {
        private readonly int _vocabularyType;
        private readonly VocabularyInfo _editingVocabulary;
        private readonly VocabularyService _vocabularyService;
        private readonly bool _isEditMode;

        public VocabularyEditWindow(int vocabularyType, VocabularyInfo editingVocabulary, VocabularyService vocabularyService)
        {
            InitializeComponent();
            _vocabularyType = vocabularyType;
            _editingVocabulary = editingVocabulary;
            _vocabularyService = vocabularyService;
            _isEditMode = editingVocabulary != null;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            // 设置窗口标题和表单类型
            switch (_vocabularyType)
            {
                case 1: // 互斥词汇
                    lblTitle.Text = _isEditMode ? "编辑互斥词汇" : "添加互斥词汇";
                    MutualForm.Visibility = Visibility.Visible;
                    ForbiddenForm.Visibility = Visibility.Collapsed;
                    break;
                case 2: // 男禁用词
                    lblTitle.Text = _isEditMode ? "编辑男禁用词" : "添加男禁用词";
                    MutualForm.Visibility = Visibility.Collapsed;
                    ForbiddenForm.Visibility = Visibility.Visible;
                    break;
                case 3: // 女禁用词
                    lblTitle.Text = _isEditMode ? "编辑女禁用词" : "添加女禁用词";
                    MutualForm.Visibility = Visibility.Collapsed;
                    ForbiddenForm.Visibility = Visibility.Visible;
                    break;
                case 4: // 全局禁用词
                    lblTitle.Text = _isEditMode ? "编辑全局禁用词" : "添加全局禁用词";
                    MutualForm.Visibility = Visibility.Collapsed;
                    ForbiddenForm.Visibility = Visibility.Visible;
                    break;
            }

            // 如果是编辑模式，填充现有数据
            if (_isEditMode && _editingVocabulary != null)
            {
                if (_vocabularyType == 1) // 互斥词汇
                {
                    if (_editingVocabulary.VocabularyName?.Contains("#") == true)
                    {
                        var parts = _editingVocabulary.VocabularyName.Split('#');
                        if (parts.Length > 0 && !string.IsNullOrWhiteSpace(parts[0]))
                        {
                            txtWordA.Text = parts[0];
                            txtWordA.Foreground = System.Windows.Media.Brushes.Black;
                        }
                        if (parts.Length > 1 && !string.IsNullOrWhiteSpace(parts[1]))
                        {
                            txtWordB.Text = parts[1];
                            txtWordB.Foreground = System.Windows.Media.Brushes.Black;
                        }
                    }
                }
                else // 禁用词
                {
                    if (!string.IsNullOrWhiteSpace(_editingVocabulary.VocabularyName))
                    {
                        txtForbiddenWord.Text = _editingVocabulary.VocabularyName;
                        txtForbiddenWord.Foreground = System.Windows.Media.Brushes.Black;
                    }
                }
                
                if (!string.IsNullOrWhiteSpace(_editingVocabulary.Remark))
                {
                    txtRemark.Text = _editingVocabulary.Remark;
                    txtRemark.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var vocabulary = new VocabularyInfo
                {
                    VocabularyType = _vocabularyType
                };

                // 根据词汇类型设置词汇名称
                if (_vocabularyType == 1) // 互斥词汇
                {
                    string wordA = txtWordA.Text.Trim();
                    string wordB = txtWordB.Text.Trim();
                    
                    // 确保不保存占位符文本
                    if (IsPlaceholderText(txtWordA))
                        wordA = "";
                    if (IsPlaceholderText(txtWordB))
                        wordB = "";
                        
                    vocabulary.VocabularyName = $"{wordA}#{wordB}";
                }
                else // 禁用词
                {
                    string forbiddenWord = txtForbiddenWord.Text.Trim();
                    
                    // 确保不保存占位符文本
                    if (IsPlaceholderText(txtForbiddenWord))
                        forbiddenWord = "";
                        
                    vocabulary.VocabularyName = forbiddenWord;
                }
                
                // 处理备注字段
                string remark = txtRemark.Text.Trim();
                if (IsPlaceholderText(txtRemark))
                    remark = "";
                vocabulary.Remark = remark;

                bool success;
                if (_isEditMode)
                {
                    vocabulary.VocabularyId = _editingVocabulary.VocabularyId;
                    success = await _vocabularyService.UpdateVocabularyAsync(vocabulary);
                }
                else
                {
                    success = await _vocabularyService.AddVocabularyAsync(vocabulary);
                }

                if (success)
                {
                    MessageBox.Show("保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    this.DialogResult = true;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("保存失败！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (_vocabularyType == 1) // 互斥词汇
            {
                // 检查词汇A是否为空或占位符
                if (string.IsNullOrWhiteSpace(txtWordA.Text) || IsPlaceholderText(txtWordA))
                {
                    MessageBox.Show("请输入词汇A！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtWordA.Focus();
                    return false;
                }

                // 检查词汇B是否为空或占位符
                if (string.IsNullOrWhiteSpace(txtWordB.Text) || IsPlaceholderText(txtWordB))
                {
                    MessageBox.Show("请输入词汇B！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtWordB.Focus();
                    return false;
                }

                string wordA = txtWordA.Text.Trim();
                string wordB = txtWordB.Text.Trim();

                if (wordA.Trim() == wordB.Trim())
                {
                    MessageBox.Show("词汇A和词汇B不能相同！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtWordB.Focus();
                    return false;
                }
            }
            else // 禁用词
            {
                if (string.IsNullOrWhiteSpace(txtForbiddenWord.Text) || IsPlaceholderText(txtForbiddenWord))
                {
                    MessageBox.Show("请输入禁用词汇！", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtForbiddenWord.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
        
        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                // 检查是否是占位符文本，如果是则清空并改为黑色
                if (IsPlaceholderText(textBox))
                {
                    textBox.Text = "";
                    textBox.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        private void TextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                // 如果文本框为空或只包含空白字符，则显示相应的占位符文本
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    SetPlaceholderText(textBox);
                }
            }
        }

        private bool IsPlaceholderText(System.Windows.Controls.TextBox textBox)
        {
            // 同时检查颜色和文本内容来判断是否是占位符
            bool isGrayColor = textBox.Foreground.ToString() == "#FF999999" || textBox.Foreground.ToString() == "#FF808080";
            
            if (!isGrayColor) return false;
            
            string text = textBox.Text;
            return text == "请输入词汇A..." || 
                   text == "请输入词汇B..." || 
                   text == "请输入禁用词汇..." || 
                   text == "请输入备注说明（可选）";
        }

        private void SetPlaceholderText(System.Windows.Controls.TextBox textBox)
        {
            textBox.Foreground = System.Windows.Media.Brushes.Gray;
            
            // 根据不同的文本框设置不同的占位符
            switch (textBox.Name)
            {
                case "txtWordA":
                    textBox.Text = "请输入词汇A...";
                    break;
                case "txtWordB":
                    textBox.Text = "请输入词汇B...";
                    break;
                case "txtForbiddenWord":
                    textBox.Text = "请输入禁用词汇...";
                    break;
                case "txtRemark":
                    textBox.Text = "请输入备注说明（可选）";
                    break;
            }
        }
    }
} 