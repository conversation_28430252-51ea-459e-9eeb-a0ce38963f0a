using System;

namespace vocabulary.Models
{
    public class VocabularyInfo
    {
        public int VocabularyId { get; set; }
        public int VocabularyType { get; set; }
        public string VocabularyName { get; set; }
        public string Remark { get; set; }
        public string DepartmentId { get; set; }
        public DateTime OperationTime { get; set; }
        public string OperatorId { get; set; }
        
        public string VocabularyTypeText
        {
            get
            {
                switch (VocabularyType)
                {
                    case 1:
                        return "互斥";
                    case 2:
                        return "男禁用";
                    case 3:
                        return "女禁用";
                    case 4:
                        return "全局禁用";
                    default:
                        return "未知";
                }
            }
        }
    }
} 