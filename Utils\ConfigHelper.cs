using System;
using System.IO;
using System.Text;

namespace vocabulary.Utils
{
    public static class ConfigHelper
    {
        public static string GetConnectionUrl()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Connection.ini");

                if (!File.Exists(configPath))
                {
                    throw new FileNotFoundException($"配置文件不存在：{configPath}");
                }

                string[] lines = File.ReadAllLines(configPath, Encoding.UTF8);
                bool inConnectionSection = false;

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 检查是否进入[Connection]节
                    if (trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = true;
                        continue;
                    }

                    // 如果遇到其他节，退出Connection节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") &&
                        !trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = false;
                        continue;
                    }

                    // 在Connection节中查找Url配置
                    if (inConnectionSection && trimmedLine.StartsWith("Url=", StringComparison.OrdinalIgnoreCase))
                    {
                        return trimmedLine.Substring(4).Trim();
                    }
                }

                throw new InvalidOperationException("在Connection.ini文件中未找到[Connection]节下的Url配置");
            }
            catch (Exception ex)
            {
                throw new Exception($"读取配置文件失败：{ex.Message}");
            }
        }
        
        public static string GetOperateLogSql()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "operatelog.xml");
                if (File.Exists(configPath))
                {
                    string xmlContent = File.ReadAllText(configPath, Encoding.UTF8);
                    // 简单的XML解析，提取SQL语句
                    int startIndex = xmlContent.IndexOf("<![CDATA[") + 9;
                    int endIndex = xmlContent.IndexOf("]]>");
                    if (startIndex > 8 && endIndex > startIndex)
                    {
                        return xmlContent.Substring(startIndex, endIndex - startIndex).Trim();
                    }
                }
                return "";
            }
            catch
            {
                return "";
            }
        }
    }
} 