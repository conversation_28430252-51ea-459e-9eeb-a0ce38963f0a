# 入室核对功能问题修复说明

## 修复的问题

### 问题1：检查开始时间问题 ✅
**问题描述**：自动模式下checkstarttime与核对时间相同
**修复方案**：
- 修改 `UpdateCheckStartAsync` 方法，增加 `isAutoMode` 参数
- 自动模式：使用 `sysdate + 随机秒数/86400` （随机1-2秒）
- 手动模式：使用 `sysdate`（当前时间）

**修改的文件**：
- `Services/PatientDataService.cs`：更新SQL语句
- `PatientCheckWindow.xaml.cs`：传递正确的模式参数

**SQL示例**：
```sql
-- 自动模式（随机增加1-2秒）
update studyinfo set checkstartid='userid', checkstarttime=sysdate + 1/86400 where checkserialnum='xxx'

-- 手动模式（当前时间）
update studyinfo set checkstartid='userid', checkstarttime=sysdate where checkserialnum='xxx'
```

### 问题2：窗口位置记忆问题 ✅
**问题描述**：窗口不会默认靠左显示，也不记录上次位置
**根本原因**：XAML中设置了 `WindowStartupLocation="CenterScreen"`，覆盖了代码设置的位置

**修复方案**：
- 将 `WindowStartupLocation` 改为 `"Manual"`
- 保持原有的位置设置和保存逻辑

**修改的文件**：
- `PatientCheckWindow.xaml`：第13行，`CenterScreen` → `Manual`

**功能验证**：
- 首次启动：窗口在左侧显示（Left=50）
- 拖动窗口后关闭：位置保存到 `WindowPosition.ini`
- 再次启动：恢复到上次位置

### 问题3：焦点获取问题 ✅
**问题描述**：1.5秒后重新获取焦点，光标闪烁但无法输入
**根本原因**：简单的 `Focus()` 方法在某些情况下不足以获得键盘输入焦点

**修复方案**：
- 使用更强制的焦点获取方法
- 先重置窗口置顶状态
- 使用 `Keyboard.Focus()` 强制设置键盘焦点
- 使用 `Dispatcher.BeginInvoke` 确保在正确的优先级执行

**修改的文件**：
- `PatientCheckWindow.xaml.cs`：`FocusTimer_Tick` 方法

**新的焦点获取流程**：
1. 重置窗口置顶状态（`Topmost = false` → `Topmost = true`）
2. 激活窗口（`Activate()` + `Focus()`）
3. 使用 `Dispatcher.BeginInvoke` 在 `Input` 优先级设置输入框焦点
4. 使用 `Keyboard.Focus()` 强制设置键盘焦点

## 技术细节

### 时间计算说明
Oracle数据库中，1天 = 86400秒，所以：
- 1秒 = 1/86400
- 2秒 = 2/86400
- 随机1-2秒：`Random.Next(1, 3)/86400`

### 窗口位置配置
`WindowPosition.ini` 文件格式：
```ini
Left=100
Top=200
```

### 焦点优先级
使用 `DispatcherPriority.Input` 确保焦点设置在输入处理优先级执行，比普通的UI更新优先级更高。

## 测试验证

### 测试1：检查开始时间
1. 设置 `checkstart=1`（自动模式）
2. 完成入室核对
3. 检查数据库：`checkstarttime` 应比 `checkverifytime` 晚1-2秒

### 测试2：窗口位置
1. 首次启动：窗口应在左侧显示
2. 拖动窗口到其他位置并关闭
3. 再次启动：窗口应在上次位置

### 测试3：焦点获取
1. 启动程序
2. 切换到其他程序
3. 1.5秒后观察：光标应闪烁且可以直接输入

## 配置文件
确保 `ConvergePACS.ini` 包含：
```ini
# 核对完成后是否开启检查开始时间记录 (0=关闭, 1=自动, 2=手动)
checkstart=0
```
