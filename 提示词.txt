


--------

这是一个新创建的wpf应用，目录下面的UI.html是该应用的原型设计图，你需要在wpf应用中根据原型设计图，实现wpf应用的UI。

然后下面是功能的具体实现：

一、、数据库连接

获取目录程序目录下的Connection.ini的[Connection]下的Url=的接口地址

下面是接口的调用方法：
1、query 方法：

处理查询操作。
从 XmlRequest 中获取 SQL 语句和参数，并执行查询。
使用 JdbcTemplate 执行查询，并使用当前类 (PacsSqlTemplate) 作为 ResultSetExtractor 来处理结果集。

【调用示例】
<request>
    <sqltype>query</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name="query1">
            <sql>SELECT sysdate FROM dual</sql>
            <input>
                <!-- 如果有参数，可以在这里添加 <parameter> 元素 -->
            </input>
            <out>
                <!-- 如果有输出参数，可以在这里添加 <parameter> 元素 -->
            </out>
        </statement>
    </statements>
</request>

【返回示例】
<?xml version='1.0' encoding='GBK'?>
<xml xmlns:s='uuid:BDC6E3F0-6DA3-11d1-A2A3-00AA00C14882' xmlns:dt='uuid:C2F41010-65B3-11d1-A29F-00AA00C14882' xmlns:rs='urn:schemas-microsoft-com:rowset' xmlns:z='#RowsetSchema'>
    <s:Schema id="RowsetSchema">
        <s:ElementType name="row" content="eltOnly" rs:CommandTimeout="30" rs:updatable="false">
            <s:AttributeType name="SYSDATE" rs:number="1">
                <s:datatype dt:type="string"/>
            </s:AttributeType>
        </s:ElementType>
    </s:Schema>
    <rs:data>
        <z:row SYSDATE="2024-08-05 10:01:39"/>
    </rs:data>
</xml>





2、update 方法：

处理更新操作。
使用事务模板 TransactionTemplate 执行更新操作。
将更新结果以 XML 格式输出。

【调用示例】

<request>
    <sqltype>update</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name="update1">
            <sql>UPDATE studyinfo SET studystatus = '50' WHERE checkserialnum= '20240706001321'</sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>


【返回示例】

<?xml version='1.0' encoding='gb2312'?>
<response>
    <result>
        <count name="update1">1</count>
    </result>
</response>


二、登录
密码使用md5形式加密，登录时把用户输入的密码加密成md5，然后通过
select userid, --用户唯一主键，后续词汇表操作人记录这个值
       departmentid, --科室ID，后续词汇表科室ID记录这个值
       (select pacsdepartment.departmentname
          from pacsdepartment 
         where pacsdepartment.departmentid = pacsuser.departmentid) as departmentname, --登录人科室名称（用于显示在界面上，先保存变量，后期使用）
       pwd, --md5形式密码
       pacsuser.username --登录人姓名，先保存变量，后期使用
  from pacsuser
 where pacsuser.loginid = '用户输入的账号'
   and pacsuser.IFFORBIDDEN = '0'

 pwd字段存储的是用户密码的md5  两者值相等表示密码正确，如果用户输入错误密码或者账号不存在，提示： 用户名或密码错误，并且连续错误三次，则结束当前进程。

三、表结构：

下面是本次词汇维护的建表语句
CREATE TABLE PACSVOCABULARYZ
(
  vocabularyid   NUMBER,
  vocabularytype NUMBER,
  vocabularyname VARCHAR2(100),
  remark VARCHAR2(32),
  departmentid    VARCHAR2(32),
  operationtime  DATE,
  operatorid     VARCHAR2(50),
  CONSTRAINT pk_vocabulary PRIMARY KEY (vocabularyid),
  CONSTRAINT uk_type_name_dept UNIQUE (vocabularytype,vocabularyname, departmentid)
)
TABLESPACE PACS55
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 64K
  NEXT 1M
);

-- 添加列的注释
COMMENT ON COLUMN PACSVOCABULARYZ.vocabularyid IS '词汇ID';
COMMENT ON COLUMN PACSVOCABULARYZ.vocabularytype IS '词汇类型 1：相斥 2：男禁用  3：女禁用 4：全局禁用';
COMMENT ON COLUMN PACSVOCABULARYZ.vocabularyname IS '词汇内容';
COMMENT ON COLUMN PACSVOCABULARYZ.remark IS '备注说明';
COMMENT ON COLUMN PACSVOCABULARYZ.departmentid IS '所属科室ID';
COMMENT ON COLUMN PACSVOCABULARYZ.operationtime IS '操作时间';
COMMENT ON COLUMN PACSVOCABULARYZ.operatorid IS '操作人ID';


CREATE SEQUENCE seq_vocabularyid START WITH 1 INCREMENT BY 1;
-- 插入时使用 seq_vocabularyid.NEXTVAL


四、互斥词
互斥词最后存储到表里，使用“#”分割，例如界面上维护的互斥词A是 左边  互斥词B是 右边 那存在vocabularyname字段的内容应该是左边#右边

五、男/女/全局禁用词
根据内容写入到表里

六：操作日志
这是一个单独的页面，不要显示在上面的登录后的主页面中，后续通过传参才能打开该页面
传参operatelog#20250501002345打开，其中20250501002345是查询参数，替换sql里的#checkserialnum#

查询sql：
读取目录下的operatelog.xml，获取里面的sql（顺便帮我创建这个文件）

<Query>
    <sql><![CDATA[
        select t.doctorcode as 操作医生,
            '查看申请单' as 事件名称,
            t.studytime as 操作时间
        from studyinfo t
        where t.checkserialnum = {checkserialnum}
        ]]></sql>
</Query>


这里要注意检查sql是否是select语句（防止恶意使用update/insert/delete等操作）


患者检查信息使用下面的sql：
select p.patientname, h.infotype,p.sex,t.studyid,t.studyscription,de.devicename,t.studyid
  from studyinfo t
 inner join patientinfo p
    on p.checkserialnum = t.checkserialnum
 inner join HISINFOTYPE h
    on h.infotypeid = p.hispatienttype
 inner join devicetypeinfo d on d.devicetypeid=t.devicetypeid
 inner join devicetable de on de.deviceid=t.deviceid
 where t.checkserialnum='20250616000928'



其他说明：
1、词汇列表中的序号，需要在渲染的时候给需要，或者sql中使用rownum，不能用vocabularyid作为序号
2、支持直接传参登录：syslogin#kshd8sudj3uhfn 其中kshd8sudj3uhfn是userid，所有登录对应的sql改成,传参登录不验证密码,查询有记录直接登录成功

select userid, --用户唯一主键，后续词汇表操作人记录这个值
       departmentid, --科室ID，后续词汇表科室ID记录这个值
       (select pacsdepartment.departmentname
          from pacsdepartment 
         where pacsdepartment.departmentid = pacsuser.departmentid) as departmentname, --登录人科室名称（用于显示在界面上，先保存变量，后期使用）
       pwd, --md5形式密码
       pacsuser.username --登录人姓名，先保存变量，后期使用
  from pacsuser
 where pacsuser.userid = '用户输入的账号'
   and pacsuser.IFFORBIDDEN = '0'
   
   
请你根据上面的需求,完成开发任务

先完成UI页面,再做功能的开发.


-----------------

编译失败：
严重性	代码	说明	项目	文件	行	抑制状态	详细信息
错误(活动)	CS8370	功能“递归模式”在 C# 7.3 中不可用。请使用 8.0 或更高的语言版本。	vocabulary	E:\vs_work\vocabulary\Models\VocabularyInfo.cs	19		
错误(活动)	CS8370	功能“递归模式”在 C# 7.3 中不可用。请使用 8.0 或更高的语言版本。	vocabulary	E:\vs_work\vocabulary\Models\VocabularyInfo.cs	25		
警告		未能找到引用的组件“System.Security.Cryptography”。	vocabulary				



-------------

输入正确的密码还是提示密码

----------

登录页面显示不全

-------------

1、去掉前面添加的测试相关的页面和代码。
2、所有新增页面都没有取消和保存按钮，检查是没显示还是没添加
3、把左边菜单栏需那种样式、以及所有页面的添加、搜索、编辑按钮统一改成前端框架Element plus的那种蓝色。
4、所有界面去除最大化按钮，只保留最小化和关闭
5、右上角的退出按钮删掉（包括相关的代码）


--------------
编辑窗口再优化一下：
1、应该是页面大小原因，按钮还是没有显示出来
2、按钮位置和大小要跟UI原型图一样，在右下角，现在是在中间，并且按钮太宽，改成正常宽度


-----------
再修改:
1.不要在底部新增一个Gird,直接把按钮放在主内容区的最后面的右下角
2.编辑界面最小化按钮也禁用

------------
DataGrid的列表太丑了，美化一下：
1、列头和数据行适当加高。
2、禁止横向滚动条
3、DataGrid的UI尽可能美化

----------
我对页面进行了微调,在此基础上,请你:
1.左侧菜单栏宽度-50

----------
这里改成显示欢迎用户，字体样式保持加粗，把原来的欢迎用户地方添加最小化和关闭按钮。关闭按钮要有移入背景。变红然后去掉窗口默认的窗口栏，把此处当作窗口栏，添加长按拖动窗口

--------------
现在有一个问题，当词汇列表的数据大于7条时，后面的数据看不到，也没有滚动条，需要添加一个垂直滚动条，但是横向的滚动条不要出现。滚动条样式尽量美观

--------

但是滚动条有点问题，如果鼠标没有放在滚动条处滚动鼠标的话，滚动条没反应。

--------
CornerRadius="4" 在编辑、删除按钮也添加圆角

--------
UI界面再优化一下:
1.搜索框和新增/修改的编辑框也添加圆角CornerRadius="4"
2.新增编辑框添加跟搜索框相同的提示效果,例如男禁用词页面的搜索框,未选中时,有灰色的文字提示:搜索男禁用词...