using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using vocabulary.Models;
using vocabulary.Services;

namespace vocabulary
{
    public partial class LoginWindow : Window
    {
        private readonly VocabularyService _vocabularyService;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;
        private readonly string _autoLoginUserId;

        /// <summary>
        /// 默认构造函数，显示正常登录界面
        /// </summary>
        public LoginWindow()
        {
            InitializeComponent();
            _vocabularyService = new VocabularyService();

            // 支持回车键登录
            txtPassword.KeyDown += (s, e) => { if (e.Key == Key.Enter) BtnLogin_Click(s, e); };
            txtUsername.KeyDown += (s, e) => { if (e.Key == Key.Enter) txtPassword.Focus(); };
        }

        /// <summary>
        /// 自动登录构造函数，用于syslogin#参数
        /// </summary>
        /// <param name="userId">要自动登录的用户ID</param>
        public LoginWindow(string userId)
        {
            InitializeComponent();
            _vocabularyService = new VocabularyService();
            _autoLoginUserId = userId;

            // 支持回车键登录
            txtPassword.KeyDown += (s, e) => { if (e.Key == Key.Enter) BtnLogin_Click(s, e); };
            txtUsername.KeyDown += (s, e) => { if (e.Key == Key.Enter) txtPassword.Focus(); };

            // 窗口加载完成后执行自动登录
            this.Loaded += LoginWindow_Loaded;
        }

        private async void LoginWindow_Loaded(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_autoLoginUserId))
            {
                await AutoLoginAsync(_autoLoginUserId);
            }
        }



        private async Task AutoLoginAsync(string userId)
        {
            try
            {
                btnLogin.IsEnabled = false;
                btnLogin.Content = "正在登录...";

                UserInfo user = await _vocabularyService.LoginByUserIdAsync(userId);
                if (user != null)
                {
                    OpenMainWindow(user);
                }
                else
                {
                    MessageBox.Show($"系统登录失败，用户不存在或已禁用！\n查询用户ID: {userId}", "错误", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"系统登录失败：{ex.Message}\n查询用户ID: {userId}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnLogin.IsEnabled = true;
                btnLogin.Content = "登录系统";
            }
        }

        private async void BtnLogin_Click(object sender, RoutedEventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Password;

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                MessageBox.Show("请输入账号和密码！", "提示", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                btnLogin.IsEnabled = false;
                btnLogin.Content = "正在登录...";

                // 添加调试信息
                string hashedPassword = vocabulary.Utils.MD5Helper.GetMD5Hash(password);
                System.Diagnostics.Debug.WriteLine($"登录尝试 - 用户名: {username}, 原密码: {password}, MD5密码: {hashedPassword}");

                UserInfo user = await _vocabularyService.LoginAsync(username, password);
                if (user != null)
                {
                    System.Diagnostics.Debug.WriteLine($"登录成功 - 用户ID: {user.UserId}, 用户名: {user.Username}, 科室: {user.DepartmentName}");
                    _loginAttempts = 0; // 登录成功，重置错误计数
                    OpenMainWindow(user);
                }
                else
                {
                    _loginAttempts++;
                    if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                    {
                        MessageBox.Show("连续登录失败3次，程序将退出！", "错误", 
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                        Application.Current.Shutdown();
                        return;
                    }
                    
                    MessageBox.Show($"用户名或密码错误！还可以尝试 {MAX_LOGIN_ATTEMPTS - _loginAttempts} 次\n" +
                                  $"输入用户名: {username}\n" +
                                  $"输入密码MD5: {hashedPassword}", 
                                  "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"登录失败：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnLogin.IsEnabled = true;
                btnLogin.Content = "登录系统";
            }
        }

        private void OpenMainWindow(UserInfo user)
        {
            MainWindow mainWindow = new MainWindow(user);
            mainWindow.Show();
            this.Close();
        }





        private void HeaderBorder_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.ButtonState == MouseButtonState.Pressed)
                {
                    this.DragMove();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"拖动窗口失败：{ex.Message}");
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗口失败：{ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _vocabularyService?.Dispose();
            base.OnClosed(e);
        }
    }
} 