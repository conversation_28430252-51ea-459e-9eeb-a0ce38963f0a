<Window x:Class="vocabulary.OperateLogWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanMinimize"
        WindowStyle="None" AllowsTransparency="True"
        Background="#f5f7fa">
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Height" Value="80"/>
            <Setter Property="Padding" Value="30,20"/>
        </Style>
        
        <Style x:Key="BackButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66b1ff"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="InfoCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,25"/>
        </Style>
        
        <!-- 滚动条样式 -->
        <Style x:Key="ScrollBarThumb" TargetType="{x:Type Thumb}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Border Background="#c1c1c1" CornerRadius="3" 
                                BorderThickness="0"/>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#a6a6a6"/>
                            </Trigger>
                            <Trigger Property="IsDragging" Value="True">
                                <Setter Property="Background" Value="#606060"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ScrollBarButton" TargetType="{x:Type RepeatButton}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Command" Value="{x:Static ScrollBar.LineUpCommand}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border Background="Transparent" Height="0" Width="0"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ScrollBarPageButton" TargetType="{x:Type RepeatButton}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border Background="Transparent"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="{x:Type ScrollBar}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0.00001*"/>
                            </Grid.RowDefinitions>
                            <Border Grid.RowSpan="1" Background="#f5f5f5" CornerRadius="3" Width="8"/>
                            <Track Name="PART_Track" Grid.Row="0" IsDirectionReversed="true">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource ScrollBarPageButton}" 
                                                Command="ScrollBar.PageUpCommand"/>
                                </Track.DecreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource ScrollBarThumb}" 
                                         Margin="2,0,2,0" Width="4"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource ScrollBarPageButton}" 
                                                Command="ScrollBar.PageDownCommand"/>
                                </Track.IncreaseRepeatButton>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="Width" Value="Auto"/>
                    <Setter Property="Height" Value="0"/>
                </Trigger>
                <Trigger Property="Orientation" Value="Vertical">
                    <Setter Property="Width" Value="8"/>
                    <Setter Property="Height" Value="Auto"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- DataGrid 样式 -->
        <Style TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#f1f5f9"/>
            <Setter Property="RowHeight" Value="50"/>
            <Setter Property="ColumnHeaderHeight" Value="50"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Disabled"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="CanUserReorderColumns" Value="False"/>
            <Setter Property="CanUserResizeColumns" Value="False"/>
            <Setter Property="CanUserSortColumns" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="AlternatingRowBackground" Value="#fafbfc"/>

        </Style>
        
        <!-- DataGrid 列头样式 -->
        <Style TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#f8f9fa"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#e5e7eb"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="Padding" Value="15,0"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridColumnHeader">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid 行样式 -->
        <Style TargetType="DataGridRow">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Height" Value="50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#f0f9ff"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#dbeafe"/>
                    <Setter Property="Foreground" Value="#1e40af"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- DataGrid 单元格样式 -->
        <Style TargetType="DataGridCell">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="Foreground" Value="#1e40af"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 头部 -->
        <Border Grid.Row="0" Style="{StaticResource HeaderStyle}" MouseLeftButtonDown="HeaderBorder_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="lblLogTitle" 
                          Text=" 操作日志 - 检查流水号：" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="#333333" VerticalAlignment="Center"/>
                
                <!-- 关闭按钮 -->
                <Button Grid.Column="1" x:Name="btnClose" 
                       Width="40" Height="40" 
                       Background="Transparent" 
                       BorderThickness="0"
                       Click="BtnBack_Click"
                       Cursor="Hand">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}" >
                                <TextBlock x:Name="textBlock" Text="✕" FontSize="16" 
                                          Foreground="#666666" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#ff4757"/>
                                    <Setter TargetName="textBlock" Property="Foreground" Value="White"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </Grid>
        </Border>
        
        <!-- 主内容区域 -->
        <StackPanel Grid.Row="1" Margin="30">
            <!-- 患者信息卡片 -->
            <Border Style="{StaticResource InfoCardStyle}">
                
                <StackPanel>
                    <TextBlock x:Name="lblPatientTitle" Text="患者姓名" FontSize="18" FontWeight="Bold" 
                      Foreground="#333333" Margin="0,0,0,20"/>
                    

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="患者类型：" FontWeight="Bold" Foreground="#4a5568" MinWidth="80"/>
                            <TextBlock x:Name="lblPatientType" Text="门诊" Foreground="#2d3748"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="患者性别：" FontWeight="Bold" Foreground="#4a5568" MinWidth="80"/>
                            <TextBlock x:Name="lblPatientSex" Text="男" Foreground="#2d3748"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Row="0" Grid.Column="2" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="检查号：" FontWeight="Bold" Foreground="#4a5568" MinWidth="80"/>
                            <TextBlock x:Name="lblStudyId" Text="CH20240115001" Foreground="#2d3748"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="检查房间：" FontWeight="Bold" Foreground="#4a5568" MinWidth="80"/>
                            <TextBlock x:Name="lblDeviceName" Text="CT室1" Foreground="#2d3748"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="检查项目：" FontWeight="Bold" Foreground="#4a5568" MinWidth="80"/>
                            <TextBlock x:Name="lblStudyDesc" Text="腹部CT增强扫描+三维重建" Foreground="#2d3748" TextWrapping="Wrap"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 操作记录卡片 -->
            <Border Style="{StaticResource InfoCardStyle}">
                
                <StackPanel>
                    <TextBlock Text=" 操作记录" FontSize="18" FontWeight="Bold" 
                      Foreground="#333333" Margin="0,0,0,20"/>
                    
                    <Border CornerRadius="8" BorderBrush="#e2e8f0" BorderThickness="1" Height="400">
                        <DataGrid x:Name="dgOperateLog" AutoGenerateColumns="False" 
                                 CanUserAddRows="False" CanUserDeleteRows="False"
                                 HeadersVisibility="Column"
                                 BorderThickness="0">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True" 
                                                   Binding="{Binding 序号}"/>
                                <DataGridTextColumn Header="操作医生" Width="200" IsReadOnly="True" 
                                                   Binding="{Binding 操作医生}"/>
                                <DataGridTextColumn Header="操作时间" Width="200" IsReadOnly="True" 
                                                   Binding="{Binding 操作时间}"/>
                                <DataGridTextColumn Header="事件名称" Width="*" IsReadOnly="True" 
                                                   Binding="{Binding 事件名称}"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>
                </StackPanel>
            </Border>
        </StackPanel>
    </Grid>
</Window> 