using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using vocabulary.Services;

namespace vocabulary
{
    public partial class OperateLogWindow : Window
    {
        private readonly string _checkSerialNum;
        private readonly OperateLogService _operateLogService;
        private ObservableCollection<OperateLogDisplayItem> _operateLogs;

        public OperateLogWindow(string checkSerialNum)
        {
            try
            {
                InitializeComponent();
                _checkSerialNum = checkSerialNum;
                _operateLogService = new OperateLogService();
                _operateLogs = new ObservableCollection<OperateLogDisplayItem>();
                
                InitializeUI();
                
                // 恢复数据加载
                this.Dispatcher.BeginInvoke(new Action(async () =>
                {
                    await LoadDataAsync();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
                
                // 暂时注释掉测试数据
                // LoadTestData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化操作日志窗口失败：{ex.Message}\n\n堆栈跟踪：\n{ex.StackTrace}", 
                              "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeUI()
        {
            try
            {
                lblLogTitle.Text = $" 操作日志 - 检查流水号：{_checkSerialNum}";
                dgOperateLog.ItemsSource = _operateLogs;
                
                // 设置默认患者信息
                lblPatientTitle.Text = "患者姓名：加载中...";
                lblPatientSex.Text = "加载中...";
                lblPatientType.Text = "加载中...";
                lblStudyId.Text = _checkSerialNum ?? "未知";
                lblStudyDesc.Text = "加载中...";
                lblDeviceName.Text = "加载中...";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化界面失败：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 恢复HTTP请求功能
        private async Task LoadDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载操作日志数据，检查号：{_checkSerialNum}");
                
                // 添加全局异常处理
                AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
                {
                    Exception ex = (Exception)args.ExceptionObject;
                    System.Diagnostics.Debug.WriteLine($"未处理异常：{ex.Message}");
                    MessageBox.Show($"程序发生未处理异常：{ex.Message}", "未处理异常", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                };
                
                // 加载患者检查信息
                try
                {
                    System.Diagnostics.Debug.WriteLine("开始加载患者信息...");
                    var patientInfo = await _operateLogService.GetPatientInfoAsync(_checkSerialNum);
                    System.Diagnostics.Debug.WriteLine("患者信息加载完成");
                    if (patientInfo != null)
                    {
                        // 更新标题显示患者姓名
                        lblPatientTitle.Text = $"患者姓名：{patientInfo.PatientName ?? "未知患者"}";
                        
                        lblPatientSex.Text = patientInfo.Sex ?? "未知";
                        lblPatientType.Text = patientInfo.InfoType ?? "未知";
                        lblStudyId.Text = patientInfo.StudyId ?? _checkSerialNum ?? "未知";
                        lblStudyDesc.Text = patientInfo.StudyScription ?? "未知";
                        lblDeviceName.Text = patientInfo.DeviceName ?? "未知";
                        
                        System.Diagnostics.Debug.WriteLine($"患者信息加载成功：{patientInfo.PatientName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("患者信息为空");
                        lblPatientTitle.Text = "患者姓名：未找到患者信息";
                        lblPatientSex.Text = "未知";
                        lblPatientType.Text = "未知";
                        lblStudyId.Text = _checkSerialNum ?? "未知";
                        lblStudyDesc.Text = "未知";
                        lblDeviceName.Text = "未知";
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"加载患者信息失败：{ex.Message}");
                    lblPatientTitle.Text = "患者姓名：加载失败";
                    lblPatientSex.Text = "未知";
                    lblPatientType.Text = "未知";
                    lblStudyId.Text = _checkSerialNum ?? "未知";
                    lblStudyDesc.Text = "未知";
                    lblDeviceName.Text = "未知";
                }

                // 加载操作日志
                try
                {
                    System.Diagnostics.Debug.WriteLine("开始加载操作日志...");
                    var logs = await _operateLogService.GetOperateLogsAsync(_checkSerialNum);
                    System.Diagnostics.Debug.WriteLine("操作日志加载完成");
                    _operateLogs.Clear();
                    
                    System.Diagnostics.Debug.WriteLine($"获取到 {logs.Count} 条操作日志");
                    
                    for (int i = 0; i < logs.Count; i++)
                    {
                        var log = logs[i];
                        _operateLogs.Add(new OperateLogDisplayItem
                        {
                            序号 = (i + 1).ToString(),
                            操作医生 = log.操作医生 ?? "未知",
                            操作时间 = log.操作时间 ?? "未知",
                            事件名称 = log.事件名称 ?? "未知"
                        });
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"最终获取到 {logs.Count} 条操作日志记录");
                    
                    if (logs.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("没有找到操作日志记录，显示提示信息");
                        // 添加一条提示信息
                        _operateLogs.Add(new OperateLogDisplayItem
                        {
                            序号 = "1",
                            操作医生 = "系统",
                            操作时间 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            事件名称 = "暂无操作记录"
                        });
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("成功加载操作日志记录");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"加载操作日志失败：{ex.Message}");
                    _operateLogs.Clear();
                    _operateLogs.Add(new OperateLogDisplayItem
                    {
                        序号 = "1",
                        操作医生 = "系统",
                        操作时间 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        事件名称 = $"加载失败：{ex.Message}"
                    });
                }
                
                System.Diagnostics.Debug.WriteLine("操作日志数据加载完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadDataAsync总体异常：{ex.Message}");
                MessageBox.Show($"加载操作日志失败：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 关闭窗口并退出应用程序
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗口失败：{ex.Message}");
            }
        }

        private void HeaderBorder_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.ButtonState == MouseButtonState.Pressed)
                {
                    this.DragMove();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"拖动窗口失败：{ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _operateLogService?.Dispose();
                // 确保应用程序退出
                Application.Current.Shutdown();
                base.OnClosed(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗口关闭清理失败：{ex.Message}");
            }
        }
    }

    // 操作日志显示项
    public class OperateLogDisplayItem
    {
        public string 序号 { get; set; }
        public string 操作医生 { get; set; }
        public string 操作时间 { get; set; }
        public string 事件名称 { get; set; }
    }
} 