using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Xml;
using vocabulary.Models;
using vocabulary.Utils;

namespace vocabulary.Services
{
    public class VocabularyService
    {
        private readonly DatabaseService _databaseService;
        private UserInfo _currentUser;

        public VocabularyService()
        {
            _databaseService = new DatabaseService();
        }

        public void SetCurrentUser(UserInfo user)
        {
            _currentUser = user;
        }

        public async Task<UserInfo> LoginAsync(string loginId, string password)
        {
            try
            {
                string hashedPassword = MD5Helper.GetMD5Hash(password);
                string sql = $@"select userid, departmentid, 
                               (select pacsdepartment.departmentname from pacsdepartment 
                                where pacsdepartment.departmentid = pacsuser.departmentid) as departmentname,
                               pwd, pacsuser.username 
                               from pacsuser 
                               where pacsuser.loginid = '{loginId}' 
                               and pacsuser.IFFORBIDDEN = '0'";

                string result = await _databaseService.ExecuteQueryAsync(sql);
                UserInfo user = ParseUserFromXml(result);
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"SQL查询结果: {result}");
                if (user != null)
                {
                    System.Diagnostics.Debug.WriteLine($"解析到用户 - ID: {user.UserId}, 用户名: {user.Username}, 数据库密码: {user.Password}");
                    System.Diagnostics.Debug.WriteLine($"输入密码MD5: {hashedPassword}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未解析到用户信息");
                }
                
                if (user != null && user.Password == hashedPassword)
                {
                    return user;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"登录失败: {ex.Message}");
            }
        }

        public async Task<UserInfo> LoginByUserIdAsync(string userId)
        {
            try
            {
                string sql = $@"select userid, departmentid, 
                               (select pacsdepartment.departmentname from pacsdepartment 
                                where pacsdepartment.departmentid = pacsuser.departmentid) as departmentname,
                               pwd, pacsuser.username 
                               from pacsuser 
                               where pacsuser.userid = '{userId}' 
                               and pacsuser.IFFORBIDDEN = '0'";

                string result = await _databaseService.ExecuteQueryAsync(sql);
                return ParseUserFromXml(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"系统登录失败: {ex.Message}");
            }
        }

        public async Task<List<VocabularyInfo>> GetVocabularyListAsync(int vocabularyType, string searchText = "")
        {
            try
            {
                string whereClause = $"vocabularytype = {vocabularyType} and departmentid = '{_currentUser?.DepartmentId}'";
                if (!string.IsNullOrEmpty(searchText))
                {
                    whereClause += $" and vocabularyname like '%{searchText}%'";
                }

                string sql = $@"select vocabularyid, vocabularytype, vocabularyname, remark, 
                               departmentid, operationtime, 
                        (select  username from pacsuser where pacsuser.userid=PACSVOCABULARYZ.operatorid ) as operatorid
                               from PACSVOCABULARYZ 
                               where {whereClause} 
                               order by operationtime desc";

                string result = await _databaseService.ExecuteQueryAsync(sql);
                return ParseVocabularyListFromXml(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取词汇列表失败: {ex.Message}");
            }
        }

        public async Task<bool> AddVocabularyAsync(VocabularyInfo vocabulary)
        {
            try
            {
                vocabulary.DepartmentId = _currentUser?.DepartmentId;
                vocabulary.OperatorId = _currentUser?.UserId;
                vocabulary.OperationTime = DateTime.Now;

                string sql = $@"insert into PACSVOCABULARYZ 
                               (vocabularyid, vocabularytype, vocabularyname, remark, 
                                departmentid, operationtime, operatorid)
                               values (seq_vocabularyid.NEXTVAL, {vocabulary.VocabularyType}, 
                                      '{vocabulary.VocabularyName}', '{vocabulary.Remark}', 
                                      '{vocabulary.DepartmentId}', sysdate, '{vocabulary.OperatorId}')";

                string result = await _databaseService.ExecuteUpdateAsync(sql);
                return CheckUpdateResult(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"添加词汇失败: {ex.Message}");
            }
        }

        public async Task<bool> UpdateVocabularyAsync(VocabularyInfo vocabulary)
        {
            try
            {
                vocabulary.OperatorId = _currentUser?.UserId;
                vocabulary.OperationTime = DateTime.Now;

                string sql = $@"update PACSVOCABULARYZ set 
                               vocabularyname = '{vocabulary.VocabularyName}',
                               remark = '{vocabulary.Remark}',
                               operationtime = sysdate,
                               operatorid = '{vocabulary.OperatorId}'
                               where vocabularyid = {vocabulary.VocabularyId}";

                string result = await _databaseService.ExecuteUpdateAsync(sql);
                return CheckUpdateResult(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"更新词汇失败: {ex.Message}");
            }
        }

        public async Task<bool> DeleteVocabularyAsync(int vocabularyId)
        {
            try
            {
                string sql = $"delete from PACSVOCABULARYZ where vocabularyid = {vocabularyId}";
                string result = await _databaseService.ExecuteUpdateAsync(sql);
                return CheckUpdateResult(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"删除词汇失败: {ex.Message}");
            }
        }

        private UserInfo ParseUserFromXml(string xmlResult)
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);
                
                // 添加命名空间管理器
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");
                
                // 首先尝试使用命名空间查找
                XmlNode dataNode = doc.SelectSingleNode("//rs:data", nsmgr);
                if (dataNode?.FirstChild != null)
                {
                    XmlNode rowNode = dataNode.FirstChild;
                    return new UserInfo
                    {
                        UserId = rowNode.Attributes["USERID"]?.Value,
                        DepartmentId = rowNode.Attributes["DEPARTMENTID"]?.Value,
                        DepartmentName = rowNode.Attributes["DEPARTMENTNAME"]?.Value,
                        Password = rowNode.Attributes["PWD"]?.Value,
                        Username = rowNode.Attributes["USERNAME"]?.Value
                    };
                }
                
                // 如果命名空间方式失败，尝试通用方式
                XmlNodeList allRows = doc.SelectNodes("//row");
                if (allRows != null && allRows.Count > 0)
                {
                    XmlNode rowNode = allRows[0];
                    return new UserInfo
                    {
                        UserId = rowNode.Attributes["USERID"]?.Value,
                        DepartmentId = rowNode.Attributes["DEPARTMENTID"]?.Value,
                        DepartmentName = rowNode.Attributes["DEPARTMENTNAME"]?.Value,
                        Password = rowNode.Attributes["PWD"]?.Value,
                        Username = rowNode.Attributes["USERNAME"]?.Value
                    };
                }
                
                return null;
            }
            catch (Exception ex)
            {
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"解析用户XML失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"XML内容: {xmlResult}");
                return null;
            }
        }

        private List<VocabularyInfo> ParseVocabularyListFromXml(string xmlResult)
        {
            List<VocabularyInfo> vocabularies = new List<VocabularyInfo>();
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);
                
                // 添加命名空间管理器
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");
                
                // 首先尝试使用命名空间查找
                XmlNodeList rowNodes = doc.SelectNodes("//z:row", nsmgr);
                
                // 如果命名空间方式失败，尝试通用方式
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    rowNodes = doc.SelectNodes("//row");
                }
                
                foreach (XmlNode rowNode in rowNodes)
                {
                    vocabularies.Add(new VocabularyInfo
                    {
                        VocabularyId = int.Parse(rowNode.Attributes["VOCABULARYID"]?.Value ?? "0"),
                        VocabularyType = int.Parse(rowNode.Attributes["VOCABULARYTYPE"]?.Value ?? "0"),
                        VocabularyName = rowNode.Attributes["VOCABULARYNAME"]?.Value,
                        Remark = rowNode.Attributes["REMARK"]?.Value,
                        DepartmentId = rowNode.Attributes["DEPARTMENTID"]?.Value,
                        OperationTime = DateTime.Parse(rowNode.Attributes["OPERATIONTIME"]?.Value ?? DateTime.Now.ToString()),
                        OperatorId = rowNode.Attributes["OPERATORID"]?.Value
                    });
                }
            }
            catch (Exception ex)
            {
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"解析词汇列表XML失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"XML内容: {xmlResult}");
            }
            return vocabularies;
        }

        private bool CheckUpdateResult(string xmlResult)
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);
                
                XmlNode countNode = doc.SelectSingleNode("//count");
                return int.Parse(countNode?.InnerText ?? "0") > 0;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _databaseService?.Dispose();
        }
    }
} 