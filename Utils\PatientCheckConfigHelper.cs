using System;
using System.IO;

namespace vocabulary.Utils
{
    /// <summary>
    /// 患者核对配置文件帮助类
    /// </summary>
    public static class PatientCheckConfigHelper
    {


        /// <summary>
        /// 获取队列验证配置（是否启用队列验证）
        /// </summary>
        /// <returns>true表示启用，false表示不启用</returns>
        public static bool GetLinkQueueConfig()
        {
            return GetConvergeConfig("linkqueue", "0") == "1";
        }

        /// <summary>
        /// 获取自动关闭配置（是否启用自动关闭）
        /// </summary>
        /// <returns>true表示启用，false表示不启用</returns>
        public static bool GetAutoCloseConfig()
        {
            return GetConvergeConfig("autoclose", "0") == "1";
        }

        /// <summary>
        /// 获取自动关闭倒计时秒数
        /// </summary>
        /// <returns>倒计时秒数（0-5秒）</returns>
        public static int GetCloseSecondsConfig()
        {
            string value = GetConvergeConfig("closesec", "0");
            if (int.TryParse(value, out int seconds))
            {
                // 限制在0-5秒范围内
                if (seconds >= 0 && seconds <= 5)
                {
                    return seconds;
                }
            }
            return 0; // 默认值或无效值时返回0
        }

        /// <summary>
        /// 获取检查开始时间记录配置
        /// </summary>
        /// <returns>0=关闭, 1=自动, 2=手动</returns>
        public static int GetCheckStartConfig()
        {
            string value = GetConvergeConfig("checkstart", "0");
            if (int.TryParse(value, out int mode))
            {
                // 限制在0-2范围内
                if (mode >= 0 && mode <= 2)
                {
                    return mode;
                }
            }
            return 0; // 默认值：关闭
        }

        /// <summary>
        /// 从ConvergePACS.ini文件读取checkverify节的配置
        /// </summary>
        /// <param name="key">配置键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        private static string GetConvergeConfig(string key, string defaultValue)
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConvergePACS.ini");

                if (!File.Exists(configPath))
                {
                    return defaultValue;
                }

                string[] lines = File.ReadAllLines(configPath);
                bool inCheckVerifySection = false;

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 跳过注释行
                    if (trimmedLine.StartsWith("#") || string.IsNullOrEmpty(trimmedLine))
                    {
                        continue;
                    }

                    // 检查是否进入[checkverify]节
                    if (trimmedLine.Equals("[checkverify]", StringComparison.OrdinalIgnoreCase))
                    {
                        inCheckVerifySection = true;
                        continue;
                    }

                    // 如果遇到其他节，退出checkverify节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") &&
                        !trimmedLine.Equals("[checkverify]", StringComparison.OrdinalIgnoreCase))
                    {
                        inCheckVerifySection = false;
                        continue;
                    }

                    // 在checkverify节中查找指定的配置
                    if (inCheckVerifySection && trimmedLine.StartsWith($"{key}=", StringComparison.OrdinalIgnoreCase))
                    {
                        return trimmedLine.Substring(key.Length + 1).Trim();
                    }
                }

                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
