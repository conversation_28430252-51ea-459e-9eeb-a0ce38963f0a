<Window x:Class="vocabulary.VocabularyEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑词汇" Height="450" Width="600"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        Background="White">
    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#4a5568"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,0"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost" 
                                            Focusable="false" 
                                            HorizontalScrollBarVisibility="Hidden" 
                                            VerticalScrollBarVisibility="Hidden"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="TextAreaStyle" TargetType="TextBox">
            <Setter Property="Height" Value="80"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="AcceptsReturn" Value="True"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Focusable="false" 
                                        HorizontalScrollBarVisibility="Hidden" 
                                        VerticalScrollBarVisibility="{TemplateBinding VerticalScrollBarVisibility}"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SaveButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66b1ff"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="CancelButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#e2e8f0"/>
            <Setter Property="Foreground" Value="#4a5568"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#cbd5e0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border Padding="30">
        <StackPanel>
            <TextBlock x:Name="lblTitle" Text="编辑词汇" FontSize="24" FontWeight="Bold" 
                      Foreground="#333333" HorizontalAlignment="Center" Margin="0,0,0,20"/>
            
            <!-- 互斥词汇表单 -->
            <Grid x:Name="MutualForm" Visibility="Visible">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <Label Content="词汇A" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtWordA" Style="{StaticResource TextBoxStyle}"
                            Text="请输入词汇A..." Foreground="#999999"
                            GotFocus="TextBox_GotFocus" LostFocus="TextBox_LostFocus"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <Label Content="词汇B" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="txtWordB" Style="{StaticResource TextBoxStyle}"
                            Text="请输入词汇B..." Foreground="#999999"
                            GotFocus="TextBox_GotFocus" LostFocus="TextBox_LostFocus"/>
                </StackPanel>
            </Grid>
            
            <!-- 禁用词表单 -->
            <StackPanel x:Name="ForbiddenForm" Visibility="Collapsed">
                <Label Content="禁用词汇" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="txtForbiddenWord" Style="{StaticResource TextBoxStyle}"
                        Text="请输入禁用词汇..." Foreground="#999999"
                        GotFocus="TextBox_GotFocus" LostFocus="TextBox_LostFocus"/>
            </StackPanel>
            
            <!-- 备注说明 -->
            <Label Content="备注说明" Style="{StaticResource LabelStyle}"/>
            <TextBox x:Name="txtRemark" Style="{StaticResource TextAreaStyle}"
                    Text="请输入备注说明（可选）" Foreground="#999999"
                    GotFocus="TextBox_GotFocus" LostFocus="TextBox_LostFocus"/>
            
            <!-- 操作按钮 - 右下角 -->
            <Grid Margin="0,30,0,0">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="取消" Style="{StaticResource CancelButtonStyle}" 
                           Margin="0,0,10,0" Click="BtnCancel_Click"/>
                    <Button Content="保存" Style="{StaticResource SaveButtonStyle}" 
                           Click="BtnSave_Click"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </Border>
</Window> 