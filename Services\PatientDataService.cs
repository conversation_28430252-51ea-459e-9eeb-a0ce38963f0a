using System;
using System.Threading.Tasks;
using System.Xml;
using vocabulary.Models;
using vocabulary.Utils;

namespace vocabulary.Services
{
    /// <summary>
    /// 患者数据服务类
    /// </summary>
    public class PatientDataService : IDisposable
    {
        private readonly DatabaseService _databaseService;

        public PatientDataService()
        {
            _databaseService = new DatabaseService();
        }

        /// <summary>
        /// 获取患者检查信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>患者检查信息</returns>
        public async Task<PatientCheckInfo> GetPatientCheckInfoAsync(string checkSerialNum, string userId)
        {
            // 使用新的视图查询患者信息
            string patientSql = $@"
                select t.typename,
                       t.patientname,
                       t.SexAndAge,
                       t.sex,
                       t.patientid,
                       t.studyid,
                       t.DeptandBed,
                       t.studyscription,
                       t.checkverifyid,
                       t.checkverifytime,
                       t.queueno
                  from v_checkverify_view t
                 where t.checkserialnum = '{checkSerialNum}'";

            // 单独查询当前登录用户
            string userSql = $@"
                select username from pacsuser where userid='{userId}'";

            try
            {
                // 使用DatabaseService执行查询
                var patientTask = _databaseService.ExecuteQueryAsync(patientSql);
                var userTask = _databaseService.ExecuteQueryAsync(userSql);

                await Task.WhenAll(patientTask, userTask);

                string patientResponse = await patientTask;
                string userResponse = await userTask;

                var patientInfo = ParsePatientCheckInfo(patientResponse);
                if (patientInfo != null)
                {
                    // 设置当前用户
                    patientInfo.CurrentUser = ParseCurrentUser(userResponse);
                }

                return patientInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"查询患者信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取队列信息
        /// </summary>
        /// <param name="studyId">检查号</param>
        /// <returns>队列信息</returns>
        public async Task<QueueInfo> GetQueueInfoAsync(string studyId)
        {
            string sql = $@"
                select t.patientname,t.sex,t.studyid from V_CURRENT_QUEUEINFO t where t.studyid='{studyId}'";

            string response = await _databaseService.ExecuteQueryAsync(sql);
            return ParseQueueInfo(response);
        }

        /// <summary>
        /// 更新核对信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateVerificationAsync(string checkSerialNum, string userId)
        {
            string sql = $@"
                update studyinfo
                set checkverifyid='{userId}', checkverifytime=sysdate
                where checkserialnum='{checkSerialNum}'";

            try
            {
                string response = await _databaseService.ExecuteUpdateAsync(sql);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 更新检查开始时间
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <param name="isAutoMode">是否为自动模式</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateCheckStartAsync(string checkSerialNum, string userId, bool isAutoMode = false)
        {
            string sql;

            if (isAutoMode)
            {
                // 自动模式：随机增加1-2秒
                Random random = new Random();
                int randomSeconds = random.Next(1, 3); // 1或2秒
                sql = $@"
                    update studyinfo
                    set checkstartid='{userId}', checkstarttime=sysdate + {randomSeconds}/86400
                    where checkserialnum='{checkSerialNum}'";
            }
            else
            {
                // 手动模式：使用当前时间
                sql = $@"
                    update studyinfo
                    set checkstartid='{userId}', checkstarttime=sysdate
                    where checkserialnum='{checkSerialNum}'";
            }

            try
            {
                string response = await _databaseService.ExecuteUpdateAsync(sql);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 解析当前用户信息
        /// </summary>
        /// <param name="xmlResponse">XML响应</param>
        /// <returns>用户名</returns>
        private string ParseCurrentUser(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return "未知用户";
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode?.Attributes?["USERNAME"] != null)
                {
                    return rowNode.Attributes["USERNAME"].Value?.Trim() ?? "未知用户";
                }

                return "未知用户";
            }
            catch
            {
                return "未知用户";
            }
        }

        private PatientCheckInfo ParsePatientCheckInfo(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 根据接口文档，查找 rs:data/z:row 节点
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    return null;
                }

                var info = new PatientCheckInfo();

                // 根据接口文档，字段作为属性存储在z:row节点中
                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper(); // 属性名通常是大写

                        switch (fieldName)
                        {
                            case "TYPENAME":
                                info.TypeName = value;
                                break;
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEXANDAGE":
                                info.SexAndAge = value;
                                break;
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "PATIENTID":
                                info.PatientId = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                            case "DEPTANDBED":
                                info.DeptAndBed = value;
                                break;
                            case "STUDYSCRIPTION":
                                info.StudyScription = value;
                                break;
                            case "CHECKVERIFYID":
                                info.CheckVerifyId = value;
                                break;
                            case "CHECKVERIFYTIME":
                                if (!string.IsNullOrEmpty(value) && DateTime.TryParse(value, out DateTime verifyTime))
                                {
                                    info.CheckVerifyTime = verifyTime;
                                }
                                break;
                            case "QUEUENO":
                                info.QueueNo = value;
                                break;
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析患者信息失败：{ex.Message}");
            }
        }

        private QueueInfo ParseQueueInfo(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 使用Microsoft Rowset格式解析
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    return null;
                }

                var info = new QueueInfo();

                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper();

                        switch (fieldName)
                        {
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析队列信息失败：{ex.Message}");
            }
        }

        public void Dispose()
        {
            _databaseService?.Dispose();
        }
    }
}
