using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using vocabulary.Models;
using vocabulary.Utils;

namespace vocabulary.Services
{
    public class OperateLogService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private bool _disposed = false;

        public OperateLogService()
        {
            _databaseService = new DatabaseService();
        }

        public async Task<PatientInfo> GetPatientInfoAsync(string checkSerialNum)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(OperateLogService));
                
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始查询患者信息，检查号：{checkSerialNum}");
                
                // 简化SQL查询，使用左连接避免数据不存在时的问题
                string sql = $@"select nvl(p.patientname, '未知') as patientname, 
                               nvl(h.infotype||d.devicetypename, '未知') as infotype, 
                               nvl(p.sex, '未知') as sex, 
                               nvl(t.studyid, '{checkSerialNum}') as studyid, 
                               nvl(t.studyscription, '未知') as studyscription, 
                               nvl(de.devicename, '未知') as devicename
                               from studyinfo t
                               left join patientinfo p on p.checkserialnum = t.checkserialnum
                               left join HISINFOTYPE h on h.infotypeid = p.hispatienttype
                               left join devicetypeinfo d on d.devicetypeid = t.devicetypeid
                               left join devicetable de on de.deviceid = t.deviceid
                               where t.checkserialnum = '{checkSerialNum}'";

                System.Diagnostics.Debug.WriteLine($"患者信息查询SQL：{sql}");
                
                string result = await _databaseService.ExecuteQueryAsync(sql);
                System.Diagnostics.Debug.WriteLine($"患者信息查询结果：{result}");
                
                var patientInfo = ParsePatientInfoFromXml(result);
                
                // 如果解析失败，返回默认信息
                if (patientInfo == null)
                {
                    patientInfo = new PatientInfo
                    {
                        PatientName = "未找到患者信息",
                        Sex = "未知",
                        InfoType = "未知",
                        StudyId = checkSerialNum,
                        StudyScription = "未知",
                        DeviceName = "未知"
                    };
                }
                
                return patientInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取患者信息异常：{ex.Message}");
                // 返回默认信息而不是抛出异常
                return new PatientInfo
                {
                    PatientName = "加载失败",
                    Sex = "未知",
                    InfoType = "未知",
                    StudyId = checkSerialNum,
                    StudyScription = "未知",
                    DeviceName = "未知"
                };
            }
        }

        public async Task<List<OperateLogInfo>> GetOperateLogsAsync(string checkSerialNum)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(OperateLogService));
                
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始查询操作日志，检查号：{checkSerialNum}");
                
                string sqlTemplate = ConfigHelper.GetOperateLogSql();
                if (string.IsNullOrEmpty(sqlTemplate))
                {
                    System.Diagnostics.Debug.WriteLine("操作日志SQL模板为空");
                    return new List<OperateLogInfo>();
                }

                System.Diagnostics.Debug.WriteLine($"操作日志SQL模板：{sqlTemplate}");

                // 替换参数
                string sql = sqlTemplate.Replace("{checkserialnum}", checkSerialNum);
                System.Diagnostics.Debug.WriteLine($"操作日志查询SQL：{sql}");

                string result = await _databaseService.ExecuteQueryAsync(sql);
                System.Diagnostics.Debug.WriteLine($"操作日志查询结果：{result}");
                
                return ParseOperateLogsFromXml(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取操作日志异常：{ex.Message}");
                // 返回空列表而不是抛出异常，避免程序崩溃
                return new List<OperateLogInfo>();
            }
        }

        private bool IsSelectQuery(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
                return false;

            // 移除注释和多余空白
            string cleanSql = Regex.Replace(sql, @"--.*$", "", RegexOptions.Multiline);
            cleanSql = Regex.Replace(cleanSql, @"/\*.*?\*/", "", RegexOptions.Singleline);
            cleanSql = cleanSql.Trim();

            // 检查是否以SELECT开头（忽略大小写）
            return cleanSql.StartsWith("select", StringComparison.OrdinalIgnoreCase);
        }

        private PatientInfo ParsePatientInfoFromXml(string xmlResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解析患者信息XML：{xmlResult}");
                
                if (string.IsNullOrEmpty(xmlResult))
                {
                    System.Diagnostics.Debug.WriteLine("患者信息XML结果为空");
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);

                // 尝试不同的XML结构
                XmlNode dataNode = doc.SelectSingleNode("//rs:data", CreateNamespaceManager(doc));
                if (dataNode == null)
                {
                    dataNode = doc.SelectSingleNode("//data");
                }
                
                if (dataNode?.FirstChild != null)
                {
                    XmlNode rowNode = dataNode.FirstChild;
                    var patientInfo = new PatientInfo
                    {
                        PatientName = GetAttributeValue(rowNode, "PATIENTNAME", "patientname"),
                        InfoType = GetAttributeValue(rowNode, "INFOTYPE", "infotype"),
                        Sex = GetAttributeValue(rowNode, "SEX", "sex"),
                        StudyId = GetAttributeValue(rowNode, "STUDYID", "studyid"),
                        StudyScription = GetAttributeValue(rowNode, "STUDYSCRIPTION", "studyscription"),
                        DeviceName = GetAttributeValue(rowNode, "DEVICENAME", "devicename")
                    };
                    
                    System.Diagnostics.Debug.WriteLine($"解析患者信息成功：{patientInfo.PatientName}");
                    return patientInfo;
                }
                
                System.Diagnostics.Debug.WriteLine("未找到患者信息数据节点");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析患者信息XML失败：{ex.Message}");
                return null;
            }
        }

        private List<OperateLogInfo> ParseOperateLogsFromXml(string xmlResult)
        {
            List<OperateLogInfo> logs = new List<OperateLogInfo>();
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始解析操作日志XML，长度：{xmlResult?.Length ?? 0}");
                System.Diagnostics.Debug.WriteLine($"XML内容：{xmlResult}");
                
                if (string.IsNullOrEmpty(xmlResult))
                {
                    System.Diagnostics.Debug.WriteLine("操作日志XML结果为空");
                    return logs;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);
                
                System.Diagnostics.Debug.WriteLine($"XML根节点：{doc.DocumentElement?.Name}");

                // 尝试不同的XML结构
                XmlNodeList rowNodes = null;
                
                // 1. 尝试z:row结构
                rowNodes = doc.SelectNodes("//z:row", CreateNamespaceManager(doc));
                System.Diagnostics.Debug.WriteLine($"z:row节点数量：{rowNodes?.Count ?? 0}");
                
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    // 2. 尝试普通row结构
                    rowNodes = doc.SelectNodes("//row");
                    System.Diagnostics.Debug.WriteLine($"row节点数量：{rowNodes?.Count ?? 0}");
                }
                
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    // 3. 尝试rs:data结构
                    XmlNode dataNode = doc.SelectSingleNode("//rs:data", CreateNamespaceManager(doc));
                    System.Diagnostics.Debug.WriteLine($"rs:data节点：{dataNode?.Name}");
                    
                    if (dataNode == null)
                    {
                        dataNode = doc.SelectSingleNode("//data");
                        System.Diagnostics.Debug.WriteLine($"data节点：{dataNode?.Name}");
                    }
                    
                    if (dataNode != null)
                    {
                        rowNodes = dataNode.ChildNodes;
                        System.Diagnostics.Debug.WriteLine($"data子节点数量：{rowNodes?.Count ?? 0}");
                    }
                }
                
                // 4. 如果还是找不到，尝试遍历所有元素节点
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("尝试遍历所有元素节点");
                    var allNodes = doc.SelectNodes("//*");
                    System.Diagnostics.Debug.WriteLine($"所有节点数量：{allNodes?.Count ?? 0}");
                    
                    if (allNodes != null)
                    {
                        foreach (XmlNode node in allNodes)
                        {
                            System.Diagnostics.Debug.WriteLine($"节点名称：{node.Name}，属性数量：{node.Attributes?.Count ?? 0}");
                            if (node.Attributes != null && node.Attributes.Count >= 3)
                            {
                                // 找到有足够属性的节点，可能是数据行
                                if (rowNodes == null)
                                {
                                    var nodeList = new List<XmlNode>();
                                    nodeList.Add(node);
                                    // 继续查找同级节点
                                    if (node.ParentNode != null)
                                    {
                                        foreach (XmlNode sibling in node.ParentNode.ChildNodes)
                                        {
                                            if (sibling.NodeType == XmlNodeType.Element && 
                                                sibling != node && 
                                                sibling.Attributes != null && 
                                                sibling.Attributes.Count >= 3)
                                            {
                                                nodeList.Add(sibling);
                                            }
                                        }
                                    }
                                    
                                    // 转换为XmlNodeList（通过临时文档）
                                    XmlDocument tempDoc = new XmlDocument();
                                    XmlElement tempRoot = tempDoc.CreateElement("temp");
                                    tempDoc.AppendChild(tempRoot);
                                    
                                    foreach (XmlNode n in nodeList)
                                    {
                                        XmlNode importedNode = tempDoc.ImportNode(n, true);
                                        tempRoot.AppendChild(importedNode);
                                    }
                                    
                                    rowNodes = tempRoot.ChildNodes;
                                    System.Diagnostics.Debug.WriteLine($"找到候选数据行：{rowNodes.Count}");
                                    break;
                                }
                            }
                        }
                    }
                }
                
                if (rowNodes != null && rowNodes.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"开始解析 {rowNodes.Count} 个数据行");
                    
                    foreach (XmlNode rowNode in rowNodes)
                    {
                        if (rowNode.NodeType == XmlNodeType.Element && rowNode.Attributes != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"处理节点：{rowNode.Name}，属性数量：{rowNode.Attributes.Count}");
                            
                            // 获取所有属性并按顺序取前三个
                            var attributes = new List<XmlAttribute>();
                            foreach (XmlAttribute attr in rowNode.Attributes)
                            {
                                attributes.Add(attr);
                                System.Diagnostics.Debug.WriteLine($"  属性：{attr.Name} = {attr.Value}");
                            }
                            
                            if (attributes.Count >= 3)
                            {
                                // 默认取前三个字段：第一个=操作医生，第二个=操作时间，第三个=事件名称
                                string 操作医生 = attributes[0].Value ?? "未知";
                                string 操作时间 = attributes[1].Value ?? "未知";
                                string 事件名称 = attributes[2].Value ?? "未知";
                                
                                System.Diagnostics.Debug.WriteLine($"解析记录：操作医生={操作医生}, 操作时间={操作时间}, 事件名称={事件名称}");
                                
                                logs.Add(new OperateLogInfo
                                {
                                    操作医生 = 操作医生,
                                    操作时间 = 操作时间,
                                    事件名称 = 事件名称
                                });
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"节点属性不足3个，跳过");
                            }
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未找到任何数据行节点");
                }
                
                System.Diagnostics.Debug.WriteLine($"解析操作日志完成，共{logs.Count}条记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析操作日志XML失败：{ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈：{ex.StackTrace}");
            }
            return logs;
        }

        private XmlNamespaceManager CreateNamespaceManager(XmlDocument doc)
        {
            XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
            nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
            nsmgr.AddNamespace("z", "#RowsetSchema");
            return nsmgr;
        }

        private string GetAttributeValue(XmlNode node, params string[] attributeNames)
        {
            if (node?.Attributes == null) return null;
            
            foreach (string attrName in attributeNames)
            {
                var attr = node.Attributes[attrName];
                if (attr != null && !string.IsNullOrEmpty(attr.Value))
                {
                    return attr.Value;
                }
            }
            return null;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _databaseService?.Dispose();
                }
                _disposed = true;
            }
        }
    }
} 