# 医疗词汇编辑系统

这是一个基于WPF的医疗词汇管理系统，用于管理医疗报告中的敏感词汇和互斥词汇。

## 项目结构

### 主要窗口文件
- `App.xaml` / `App.xaml.cs` - 应用程序入口和配置
- `LoginWindow.xaml` / `LoginWindow.xaml.cs` - 登录窗口
- `MainWindow.xaml` / `MainWindow.xaml.cs` - 主窗口，包含词汇管理界面
- `VocabularyEditWindow.xaml` / `VocabularyEditWindow.xaml.cs` - 词汇编辑弹窗
- `OperateLogWindow.xaml` / `OperateLogWindow.xaml.cs` - 操作日志查看窗口

### 数据模型 (Models/)
- `UserInfo.cs` - 用户信息模型
- `VocabularyInfo.cs` - 词汇信息模型
- `PatientInfo.cs` - 患者信息模型

### 服务层 (Services/)
- `DatabaseService.cs` - 数据库服务，通过HTTP接口与后端数据库通信
- `VocabularyService.cs` - 词汇相关业务逻辑服务
- `OperateLogService.cs` - 操作日志服务

### 工具类 (Utils/)
- `ConfigHelper.cs` - 配置文件读取工具
- `MD5Helper.cs` - MD5加密工具

### 配置文件
- `Connection.ini` - 数据库连接配置，包含后端数据库接口URL
- `operatelog.xml` - 操作日志查询的SQL配置
- `App.config` - 应用程序配置文件
- `vocabulary.csproj` - 项目文件

## 功能特性

1. **用户登录**
   - MD5******加密
   - 登录失败次数限制（3次）
   - 支持参数化登录（syslogin#用户ID）

2. **词汇管理**
   - 添加、编辑、删除词汇
   - 支持四种词汇类型：
     - 互斥词汇（存储格式：词汇A#词汇B）
     - 男性禁用词汇
     - 女性禁用词汇
     - 全局禁用词汇

3. **操作日志**
   - 查看操作历史记录
   - 支持参数化访问（operatelog#检查序列号）

## 配置说明

### Connection.ini
```ini
[Connection]
Url=http://数据库接口地址/PacsDBProxy/PacsDatabase/PacsDatabase.action
```

### operatelog.xml
包含操作日志查询的SQL语句，使用`{checkserialnum}`参数占位符。

## 使用方法

1. **配置数据库连接**
   - 修改`Connection.ini`文件中的URL为实际的数据库接口地址

2. **启动应用程序**
   - 编译并运行`vocabulary.exe`
   - 首先会显示登录窗口

3. **登录系统**
   - 输入用户名和******
   - 或使用参数启动：`vocabulary.exe syslogin#用户ID`

4. **管理词汇**
   - 在主窗口中选择词汇类型
   - 使用"新增"按钮添加词汇
   - 双击列表项编辑现有词汇

5. **查看操作日志**
   - 使用参数启动：`vocabulary.exe operatelog#检查序列号`
   - 或从主窗口导航到操作日志页面

## 技术说明

- **框架**: WPF (.NET Framework)
- **数据通信**: 通过HTTP POST发送XML格式请求到后端数据库接口
- **编码处理**: 支持GBK/UTF-8编码转换
- **安全**: MD5******加密，SQL注入防护

## 项目文件

- `vocabulary.sln` - Visual Studio解决方案文件
- `1.ico` - 应用程序图标
- `UI.html` - 原型设计参考文件（开发参考用）

## 🚀 主要功能

### 1. 词汇编辑系统
- **词汇管理**：增删改查医学词汇条目
- **用户认证**：支持用户名******登录和自动登录
- **权限控制**：基于用户角色的权限管理
- **数据同步**：与数据库实时同步词汇数据

### 2. 操作日志系统
- **日志查看**：查看指定检查流水号的操作记录
- **患者信息**：显示患者基本信息和检查详情
- **操作追踪**：记录操作医生、时间和事件详情
- **独立启动**：支持直接启动操作日志窗口

## 📋 系统要求

- .NET Framework 4.8
- Windows 10 或更高版本
- 支持的数据库：SQL Server、Oracle等

## 🔧 启动参数

系统支持多种启动参数，实现不同的功能模式：

### 1. 系统自动登录
```bash
vocabulary.exe syslogin#[用户ID]
```
**功能说明**：
- 绕过登录界面，直接使用指定用户ID登录系统
- 打开主词汇编辑界面
- 适用于集成到其他医疗系统中

**示例**：
```bash
vocabulary.exe syslogin#12345
```

### 2. 操作日志查看
```bash
vocabulary.exe operatelog#[检查流水号]
```
**功能说明**：
- 直接打开操作日志窗口
- 显示指定检查流水号的操作记录
- 隐藏主登录窗口，只显示日志界面
- 适用于从其他系统调用查看操作记录

**示例**：
```bash
vocabulary.exe operatelog#20250527002234
```

### 3. 普通启动
```bash
vocabulary.exe
```
**功能说明**：
- 显示登录界面
- 用户手动输入账号******登录
- 进入完整的词汇编辑系统

## ⚙️ 配置文件

### 1. Connection.ini
数据库连接配置文件，位于程序根目录。

**文件结构**：
```ini
[Database]
ConnectionString=Data Source=服务器地址;Initial Catalog=数据库名;User ID=用户名;Password=******;
Provider=SqlServer
Timeout=30

[Settings]
AutoBackup=true
BackupInterval=60
LogLevel=Info
```

**配置项说明**：
- `ConnectionString`：数据库连接字符串
- `Provider`：数据库提供程序（SqlServer/Oracle/MySQL）
- `Timeout`：连接超时时间（秒）
- `AutoBackup`：是否自动备份
- `BackupInterval`：备份间隔（分钟）
- `LogLevel`：日志级别（Debug/Info/Warning/Error）

### 2. operatelog.xml
操作日志查询配置文件，定义日志数据的获取方式。

**文件结构**：
```xml
<?xml version="1.0" encoding="utf-8"?>
<OperateLogConfig>
    <!-- 患者信息查询配置 -->
    <PatientInfo>
        <Url>http://localhost:8080/api/patient/info</Url>
        <Method>POST</Method>
        <Timeout>30000</Timeout>
        <Parameters>
            <Parameter name="checkSerialNum" type="string"/>
        </Parameters>
    </PatientInfo>
    
    <!-- 操作日志查询配置 -->
    <OperateLog>
        <Url>http://localhost:8080/api/operate/log</Url>
        <Method>POST</Method>
        <Timeout>30000</Timeout>
        <Parameters>
            <Parameter name="checkSerialNum" type="string"/>
        </Parameters>
    </OperateLog>
    
    <!-- 响应数据编码 -->
    <Encoding>UTF-8</Encoding>
    
    <!-- 重试配置 -->
    <Retry>
        <MaxAttempts>3</MaxAttempts>
        <DelaySeconds>2</DelaySeconds>
    </Retry>
</OperateLogConfig>
```

**配置项说明**：
- `PatientInfo/Url`：患者信息查询接口地址
- `OperateLog/Url`：操作日志查询接口地址
- `Method`：HTTP请求方法（GET/POST）
- `Timeout`：请求超时时间（毫秒）
- `Encoding`：响应数据编码格式
- `Retry`：重试机制配置

## 🔌 API接口

### 患者信息接口
**请求**：
```http
POST /api/patient/info
Content-Type: application/x-www-form-urlencoded

checkSerialNum=20250527002234
```

**响应**：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<PatientInfo>
    <PatientName>张三</PatientName>
    <Sex>男</Sex>
    <InfoType>门诊</InfoType>
    <StudyId>CH20240115001</StudyId>
    <StudyScription>腹部CT增强扫描+三维重建</StudyScription>
    <DeviceName>CT室1</DeviceName>
</PatientInfo>
```

### 操作日志接口
**请求**：
```http
POST /api/operate/log
Content-Type: application/x-www-form-urlencoded

checkSerialNum=20250527002234
```

**响应**：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<OperateLogList>
    <OperateLog>
        <Doctor>李医生</Doctor>
        <Time>2025-01-27 10:30:15</Time>
        <Event>开始检查</Event>
    </OperateLog>
    <OperateLog>
        <Doctor>王医生</Doctor>
        <Time>2025-01-27 10:45:22</Time>
        <Event>完成扫描</Event>
    </OperateLog>
</OperateLogList>
```

## 🎨 界面特色

### 登录界面
- **现代化设计**：无边框透明窗口
- **蓝白渐变**：使用Element Plus标准蓝色主题
- **自定义头部**：支持拖动和关闭功能
- **响应式交互**：悬停效果和焦点状态

### 操作日志界面
- **患者信息卡片**：清晰展示患者基本信息
- **操作记录表格**：带滚动条的操作日志列表
- **自适应布局**：检查项目字段支持长文本显示
- **居中对齐**：所有内容统一居中对齐

## 🔧 部署说明

### 1. 程序部署
1. 将编译后的程序文件复制到目标目录
2. 确保目录包含以下文件：
   - `vocabulary.exe`（主程序）
   - `Connection.ini`（数据库配置）
   - `operatelog.xml`（日志配置）
   - 相关DLL依赖文件

### 2. 数据库配置
1. 修改`Connection.ini`中的数据库连接信息
2. 确保数据库服务器可访问
3. 验证用户权限和表结构

### 3. API服务配置
1. 部署操作日志查询API服务
2. 修改`operatelog.xml`中的接口地址
3. 确保API服务返回正确的XML格式数据

## 🚨 故障排除

### 常见问题

**1. 数据库连接失败**
- 检查`Connection.ini`配置是否正确
- 验证数据库服务器是否可访问
- 确认用户名******是否有效

**2. 操作日志加载失败**
- 检查`operatelog.xml`中的API地址
- 验证API服务是否正常运行
- 查看返回的XML格式是否正确

**3. 程序启动失败**
- 确认.NET Framework 4.8已安装
- 检查程序文件完整性
- 查看Windows事件日志获取详细错误信息

**4. 界面显示异常**
- 确认显示器分辨率设置
- 检查Windows显示缩放比例
- 重启程序尝试恢复

## 📝 更新日志

### v2.0.0 (2025-01-27)
- ✨ 新增操作日志查看功能
- 🎨 重新设计登录界面，采用Element Plus蓝色主题
- 🔧 支持启动参数自动登录和日志查看
- 📱 优化界面布局和用户体验
- 🛠️ 增强错误处理和异常管理

### v1.0.0
- 🎉 初始版本发布
- 📚 基础词汇编辑功能
- 👤 用户认证系统
- 💾 数据库集成

## 📞 技术支持

如需技术支持或功能建议，请联系开发团队。

---

© 2025 医学词汇编辑系统. 保留所有权利。 