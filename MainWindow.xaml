﻿<Window x:Class="vocabulary.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:vocabulary"
        mc:Ignorable="d"
        Title="医学词汇编辑系统" Height="800" Width="1250"
        WindowStartupLocation="CenterScreen" ResizeMode="CanMinimize"
        WindowStyle="None" AllowsTransparency="True"
        Background="Transparent">
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Height" Value="70"/>
            <Setter Property="Padding" Value="30,15"/>
        </Style>
        
        <Style x:Key="SidebarStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
        </Style>
        
        <Style x:Key="NavButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,0,4,0"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Padding" Value="25,15"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#ecf5ff"/>
                                <Setter Property="Foreground" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ActiveNavButtonStyle" TargetType="Button" BasedOn="{StaticResource NavButtonStyle}">
            <Setter Property="Background" Value="#ecf5ff"/>
            <Setter Property="Foreground" Value="#409eff"/>
            <Setter Property="BorderBrush" Value="#409eff"/>
        </Style>
        
        <Style x:Key="ContentAreaStyle" TargetType="Border">
            <Setter Property="Background" Value="#f5f7fa"/>
            <Setter Property="Padding" Value="30"/>
        </Style>
        
        <Style x:Key="PageStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="30"/>
        </Style>
        
        <Style x:Key="AddButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66b1ff"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SearchButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66b1ff"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid 操作按钮样式 -->
        <Style x:Key="EditButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#409eff"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66b1ff"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3a8ee6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="DeleteButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#f56565"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#f78a8a"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#e53e3e"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#e5e5e5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#e81123"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid 样式 -->
        <Style TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#f1f5f9"/>
            <Setter Property="RowHeight" Value="50"/>
            <Setter Property="ColumnHeaderHeight" Value="50"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Disabled"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="CanUserReorderColumns" Value="False"/>
            <Setter Property="CanUserResizeColumns" Value="False"/>
            <Setter Property="CanUserSortColumns" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="AlternatingRowBackground" Value="#fafbfc"/>
        </Style>
        
        <!-- DataGrid 列头样式 -->
        <Style TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#f8f9fa"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#e5e7eb"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="Padding" Value="15,0"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridColumnHeader">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid 行样式 -->
        <Style TargetType="DataGridRow">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Height" Value="50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#f0f9ff"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#dbeafe"/>
                    <Setter Property="Foreground" Value="#1e40af"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- DataGrid 单元格样式 -->
        <Style TargetType="DataGridCell">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="Foreground" Value="#1e40af"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 滚动条样式 -->
        <Style x:Key="ScrollBarThumb" TargetType="{x:Type Thumb}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Border Background="#c1c1c1" CornerRadius="3" 
                                BorderThickness="0"/>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#a6a6a6"/>
                            </Trigger>
                            <Trigger Property="IsDragging" Value="True">
                                <Setter Property="Background" Value="#606060"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ScrollBarButton" TargetType="{x:Type RepeatButton}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Command" Value="{x:Static ScrollBar.LineUpCommand}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border Background="Transparent" Height="0" Width="0"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ScrollBarPageButton" TargetType="{x:Type RepeatButton}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type RepeatButton}">
                        <Border Background="Transparent"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="{x:Type ScrollBar}">
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="0.00001*"/>
                            </Grid.RowDefinitions>
                            <Border Grid.RowSpan="1" Background="#f5f5f5" CornerRadius="3" Width="8"/>
                            <Track Name="PART_Track" Grid.Row="0" IsDirectionReversed="true">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource ScrollBarPageButton}" 
                                                Command="ScrollBar.PageUpCommand"/>
                                </Track.DecreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource ScrollBarThumb}" 
                                         Margin="2,0,2,0" Width="4"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource ScrollBarPageButton}" 
                                                Command="ScrollBar.PageDownCommand"/>
                                </Track.IncreaseRepeatButton>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="Width" Value="Auto"/>
                    <Setter Property="Height" Value="0"/>
                </Trigger>
                <Trigger Property="Orientation" Value="Vertical">
                    <Setter Property="Width" Value="8"/>
                    <Setter Property="Height" Value="Auto"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- TextBox 圆角样式 -->
        <Style x:Key="RoundedTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="15,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost" 
                                            Focusable="false" 
                                            HorizontalScrollBarVisibility="Hidden" 
                                            VerticalScrollBarVisibility="Hidden"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#409eff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
        <Border Background="White" CornerRadius="8">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="0" Color="#33000000"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 头部 -->
            <Border Grid.Row="0" Style="{StaticResource HeaderStyle}" 
                    CornerRadius="8,8,0,0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock x:Name="lblUserInfo" Text="欢迎用户" FontSize="24" FontWeight="Bold" 
                                  Foreground="#333333" VerticalAlignment="Center"/>
                    </StackPanel>
                    
                   
                    
                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="btnMinimize" Content="&#xE921;" 
                               Style="{StaticResource WindowControlButtonStyle}"
                               Click="BtnMinimize_Click"/>
                        <Button x:Name="btnClose" Content="&#xE8BB;" 
                               Style="{StaticResource CloseButtonStyle}"
                               Click="BtnClose_Click"/>
                    </StackPanel>
                </Grid>
            </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 侧边栏 -->
            <Border Grid.Column="0" Style="{StaticResource SidebarStyle}">
                <StackPanel Margin="0,20,0,0">
                    <Button x:Name="btnMutual" Content=" 互斥词汇" 
                           Style="{StaticResource ActiveNavButtonStyle}"
                           Click="BtnNavigation_Click" Tag="mutual"/>
                    <Button x:Name="btnMale" Content=" 男禁用词" 
                           Style="{StaticResource NavButtonStyle}"
                           Click="BtnNavigation_Click" Tag="male"/>
                    <Button x:Name="btnFemale" Content=" 女禁用词" 
                           Style="{StaticResource NavButtonStyle}"
                           Click="BtnNavigation_Click" Tag="female"/>
                    <Button x:Name="btnGlobal" Content=" 全局禁用词" 
                           Style="{StaticResource NavButtonStyle}"
                           Click="BtnNavigation_Click" Tag="global"/>
                </StackPanel>
            </Border>
            
            <!-- 内容区域 -->
            <Border Grid.Column="1" Style="{StaticResource ContentAreaStyle}">
                <Grid>
                    <!-- 互斥词汇编辑页面 -->
                    <Border x:Name="MutualPage" Style="{StaticResource PageStyle}">
                        <StackPanel>
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text=" 互斥词汇编辑" 
                                          FontSize="24" FontWeight="Bold" 
                                          Foreground="#333333" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Content=" 新增互斥词汇" 
                                       Style="{StaticResource AddButtonStyle}"
                                       Click="BtnAddMutual_Click"/>
                            </Grid>
                            
                            <Border Background="#e3f2fd" BorderBrush="#667eea" 
                                   BorderThickness="4,0,0,0" Padding="15,15,20,15" 
                                   Margin="0,0,0,25" CornerRadius="0,8,8,0">
                                <TextBlock Text="当报告描述、诊断同时出现词汇A和词汇B，则会在提交报告的时候给出提醒。" 
                                          Foreground="#4a5568" FontSize="14"/>
                            </Border>
                            
                            <Grid Margin="0,0,0,25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="txtMutualSearch" Grid.Column="0" 
                                        Style="{StaticResource RoundedTextBoxStyle}"
                                        Text="搜索互斥词汇..." Foreground="#999999"
                                        GotFocus="SearchBox_GotFocus" 
                                        LostFocus="SearchBox_LostFocus"/>
                                <Button Grid.Column="1" Content="🔍 搜索" 
                                       Style="{StaticResource SearchButtonStyle}"
                                       Margin="15,0,0,0" Click="BtnSearch_Click"/>
                            </Grid>
                            
                            <DataGrid x:Name="dgMutual" AutoGenerateColumns="False" 
                                     CanUserAddRows="False" CanUserDeleteRows="False"
                                     HeadersVisibility="Column" MaxHeight="400"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Disabled">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True" 
                                                       Binding="{Binding 序号}"/>
                                    <DataGridTextColumn Header="词汇A" Width="130" IsReadOnly="True" 
                                                       Binding="{Binding 词汇A}"/>
                                    <DataGridTextColumn Header="词汇B" Width="130" IsReadOnly="True" 
                                                       Binding="{Binding 词汇B}"/>
                                    <DataGridTextColumn Header="备注说明" Width="*" IsReadOnly="True" 
                                                       Binding="{Binding Remark}"/>
                                    <DataGridTextColumn Header="操作时间" Width="175" IsReadOnly="True" 
                                                       Binding="{Binding 操作时间}"/>
                                    <DataGridTextColumn Header="操作人" Width="120" IsReadOnly="True" 
                                                       Binding="{Binding 操作人}"/>
                                    <DataGridTemplateColumn Header="操作" Width="170">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="✏️ 编辑" 
                                                           Style="{StaticResource EditButtonStyle}"
                                                           Margin="0,0,5,0"
                                                           Click="BtnEdit_Click"/>
                                                    <Button Content="🗑️ 删除" 
                                                           Style="{StaticResource DeleteButtonStyle}"
                                                           Click="BtnDelete_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                    
                    <!-- 男禁用词页面 -->
                    <Border x:Name="MalePage" Style="{StaticResource PageStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text=" 男禁用词管理" 
                                          FontSize="24" FontWeight="Bold" 
                                          Foreground="#333333" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Content=" 新增男禁用词" 
                                       Style="{StaticResource AddButtonStyle}"
                                       Click="BtnAddMale_Click"/>
                            </Grid>
                            
                            <Border Background="#e3f2fd" BorderBrush="#667eea" 
                                   BorderThickness="4,0,0,0" Padding="15,15,20,15" 
                                   Margin="0,0,0,25" CornerRadius="0,8,8,0">
                                <TextBlock Text="当患者性别为男时，报告描述、诊断出现男禁用词中的词汇时，将会禁止提交报告。" 
                                          Foreground="#4a5568" FontSize="14"/>
                            </Border>
                            
                            <Grid Margin="0,0,0,25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="txtMaleSearch" Grid.Column="0" 
                                        Style="{StaticResource RoundedTextBoxStyle}"
                                        Text="搜索男禁用词..." Foreground="#999999"
                                        GotFocus="SearchBox_GotFocus" 
                                        LostFocus="SearchBox_LostFocus"/>
                                <Button Grid.Column="1" Content="🔍 搜索" 
                                       Style="{StaticResource SearchButtonStyle}"
                                       Margin="15,0,0,0" Click="BtnSearch_Click"/>
                            </Grid>
                            
                            <DataGrid x:Name="dgMale" AutoGenerateColumns="False" 
                                     CanUserAddRows="False" CanUserDeleteRows="False"
                                     HeadersVisibility="Column" MaxHeight="400"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Disabled">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True" 
                                                       Binding="{Binding 序号}"/>
                                    <DataGridTextColumn Header="禁用词汇" Width="200" IsReadOnly="True" 
                                                       Binding="{Binding 禁用词汇}"/>
                                    <DataGridTextColumn Header="备注说明" Width="*" IsReadOnly="True" 
                                                       Binding="{Binding Remark}"/>
                                    <DataGridTextColumn Header="操作时间" Width="180" IsReadOnly="True" 
                                                       Binding="{Binding 操作时间}"/>
                                    <DataGridTextColumn Header="操作人" Width="120" IsReadOnly="True" 
                                                       Binding="{Binding 操作人}"/>
                                    <DataGridTemplateColumn Header="操作" Width="180">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="✏️ 编辑" 
                                                           Style="{StaticResource EditButtonStyle}"
                                                           Margin="0,0,5,0"
                                                           Click="BtnEdit_Click"/>
                                                    <Button Content="🗑️ 删除" 
                                                           Style="{StaticResource DeleteButtonStyle}"
                                                           Click="BtnDelete_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                    
                    <!-- 女禁用词页面 -->
                    <Border x:Name="FemalePage" Style="{StaticResource PageStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text=" 女禁用词管理" 
                                          FontSize="24" FontWeight="Bold" 
                                          Foreground="#333333" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Content=" 新增女禁用词" 
                                       Style="{StaticResource AddButtonStyle}"
                                       Click="BtnAddFemale_Click"/>
                            </Grid>
                            
                            <Border Background="#e3f2fd" BorderBrush="#667eea" 
                                   BorderThickness="4,0,0,0" Padding="15,15,20,15" 
                                   Margin="0,0,0,25" CornerRadius="0,8,8,0">
                                <TextBlock Text="当患者性别为女时，报告描述、诊断出现女禁用词中的词汇时，将会禁止提交报告。" 
                                          Foreground="#4a5568" FontSize="14"/>
                            </Border>
                            
                            <Grid Margin="0,0,0,25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="txtFemaleSearch" Grid.Column="0" 
                                        Style="{StaticResource RoundedTextBoxStyle}"
                                        Text="搜索女禁用词..." Foreground="#999999"
                                        GotFocus="SearchBox_GotFocus" 
                                        LostFocus="SearchBox_LostFocus"/>
                                <Button Grid.Column="1" Content="🔍 搜索" 
                                       Style="{StaticResource SearchButtonStyle}"
                                       Margin="15,0,0,0" Click="BtnSearch_Click"/>
                            </Grid>
                            
                            <DataGrid x:Name="dgFemale" AutoGenerateColumns="False" 
                                     CanUserAddRows="False" CanUserDeleteRows="False"
                                     HeadersVisibility="Column" MaxHeight="400"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Disabled">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True" 
                                                       Binding="{Binding 序号}"/>
                                    <DataGridTextColumn Header="禁用词汇" Width="200" IsReadOnly="True" 
                                                       Binding="{Binding 禁用词汇}"/>
                                    <DataGridTextColumn Header="备注说明" Width="*" IsReadOnly="True" 
                                                       Binding="{Binding Remark}"/>
                                    <DataGridTextColumn Header="操作时间" Width="180" IsReadOnly="True" 
                                                       Binding="{Binding 操作时间}"/>
                                    <DataGridTextColumn Header="操作人" Width="120" IsReadOnly="True" 
                                                       Binding="{Binding 操作人}"/>
                                    <DataGridTemplateColumn Header="操作" Width="180">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="✏️ 编辑" 
                                                           Style="{StaticResource EditButtonStyle}"
                                                           Margin="0,0,5,0"
                                                           Click="BtnEdit_Click"/>
                                                    <Button Content="🗑️ 删除" 
                                                           Style="{StaticResource DeleteButtonStyle}"
                                                           Click="BtnDelete_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                    
                    <!-- 全局禁用词页面 -->
                    <Border x:Name="GlobalPage" Style="{StaticResource PageStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text=" 全局禁用词管理" 
                                          FontSize="24" FontWeight="Bold" 
                                          Foreground="#333333" VerticalAlignment="Center"/>
                                <Button Grid.Column="1" Content=" 新增全局禁用词" 
                                       Style="{StaticResource AddButtonStyle}"
                                       Click="BtnAddGlobal_Click"/>
                            </Grid>
                            
                            <Border Background="#e3f2fd" BorderBrush="#667eea" 
                                   BorderThickness="4,0,0,0" Padding="15,15,20,15" 
                                   Margin="0,0,0,25" CornerRadius="0,8,8,0">
                                <TextBlock Text="当报告描述、诊断出现全局禁用词时，将会禁止提交报告。" 
                                          Foreground="#4a5568" FontSize="14"/>
                            </Border>
                            
                            <Grid Margin="0,0,0,25">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="txtGlobalSearch" Grid.Column="0" 
                                        Style="{StaticResource RoundedTextBoxStyle}"
                                        Text="搜索全局禁用词..." Foreground="#999999"
                                        GotFocus="SearchBox_GotFocus" 
                                        LostFocus="SearchBox_LostFocus"/>
                                <Button Grid.Column="1" Content="🔍 搜索" 
                                       Style="{StaticResource SearchButtonStyle}"
                                       Margin="15,0,0,0" Click="BtnSearch_Click"/>
                            </Grid>
                            
                            <DataGrid x:Name="dgGlobal" AutoGenerateColumns="False" 
                                     CanUserAddRows="False" CanUserDeleteRows="False"
                                     HeadersVisibility="Column" MaxHeight="400"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Disabled">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True" 
                                                       Binding="{Binding 序号}"/>
                                    <DataGridTextColumn Header="禁用词汇" Width="200" IsReadOnly="True" 
                                                       Binding="{Binding 禁用词汇}"/>
                                    <DataGridTextColumn Header="备注说明" Width="*" IsReadOnly="True" 
                                                       Binding="{Binding Remark}"/>
                                    <DataGridTextColumn Header="操作时间" Width="180" IsReadOnly="True" 
                                                       Binding="{Binding 操作时间}"/>
                                    <DataGridTextColumn Header="操作人" Width="120" IsReadOnly="True" 
                                                       Binding="{Binding 操作人}"/>
                                    <DataGridTemplateColumn Header="操作" Width="180">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="✏️ 编辑" 
                                                           Style="{StaticResource EditButtonStyle}"
                                                           Margin="0,0,5,0"
                                                           Click="BtnEdit_Click"/>
                                                    <Button Content="🗑️ 删除" 
                                                           Style="{StaticResource DeleteButtonStyle}"
                                                           Click="BtnDelete_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>
        </Grid>
    </Border>
</Window>
