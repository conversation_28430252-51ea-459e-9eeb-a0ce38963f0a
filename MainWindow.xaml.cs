﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using vocabulary.Models;
using vocabulary.Services;

namespace vocabulary
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly VocabularyService _vocabularyService;
        private UserInfo _currentUser;
        private ObservableCollection<VocabularyDisplayItem> _currentVocabularies;
        private int _currentVocabularyType = 1; // 默认显示互斥词汇
        private string _currentSearchText = "";

        public MainWindow(UserInfo user)
        {
            InitializeComponent();
            _currentUser = user;
            _vocabularyService = new VocabularyService();
            _vocabularyService.SetCurrentUser(user);
            _currentVocabularies = new ObservableCollection<VocabularyDisplayItem>();
            
            InitializeUI();
            _ = LoadVocabularyDataAsync();
        }

        private void InitializeUI()
        {
            lblUserInfo.Text = $"欢迎，{_currentUser.Username} ({_currentUser.DepartmentName})";
            dgMutual.ItemsSource = _currentVocabularies;
            dgMale.ItemsSource = _currentVocabularies;
            dgFemale.ItemsSource = _currentVocabularies;
            dgGlobal.ItemsSource = _currentVocabularies;
        }

        private async Task LoadVocabularyDataAsync()
        {
            try
            {
                var vocabularies = await _vocabularyService.GetVocabularyListAsync(_currentVocabularyType, _currentSearchText);
                _currentVocabularies.Clear();
                
                for (int i = 0; i < vocabularies.Count; i++)
                {
                    var vocab = vocabularies[i];
                    var displayItem = new VocabularyDisplayItem
                    {
                        序号 = (i + 1).ToString(),
                        VocabularyId = vocab.VocabularyId,
                        VocabularyType = vocab.VocabularyType,
                        VocabularyName = vocab.VocabularyName,
                        Remark = vocab.Remark,
                        操作时间 = vocab.OperationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        操作人 = vocab.OperatorId
                    };

                    // 处理互斥词汇显示
                    if (vocab.VocabularyType == 1 && vocab.VocabularyName?.Contains("#") == true)
                    {
                        var parts = vocab.VocabularyName.Split('#');
                        displayItem.词汇A = parts.Length > 0 ? parts[0] : "";
                        displayItem.词汇B = parts.Length > 1 ? parts[1] : "";
                    }
                    else
                    {
                        displayItem.禁用词汇 = vocab.VocabularyName;
                    }

                    _currentVocabularies.Add(displayItem);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnNavigation_Click(object sender, RoutedEventArgs e)
        {
            Button clickedButton = sender as Button;
            string tag = clickedButton?.Tag?.ToString();

            // 重置所有导航按钮样式
            btnMutual.Style = (Style)FindResource("NavButtonStyle");
            btnMale.Style = (Style)FindResource("NavButtonStyle");
            btnFemale.Style = (Style)FindResource("NavButtonStyle");
            btnGlobal.Style = (Style)FindResource("NavButtonStyle");

            // 设置当前按钮为激活状态
            clickedButton.Style = (Style)FindResource("ActiveNavButtonStyle");

            // 隐藏所有页面
            MutualPage.Visibility = Visibility.Collapsed;
            MalePage.Visibility = Visibility.Collapsed;
            FemalePage.Visibility = Visibility.Collapsed;
            GlobalPage.Visibility = Visibility.Collapsed;

            // 显示对应页面并设置词汇类型
            switch (tag)
            {
                case "mutual":
                    MutualPage.Visibility = Visibility.Visible;
                    _currentVocabularyType = 1;
                    break;
                case "male":
                    MalePage.Visibility = Visibility.Visible;
                    _currentVocabularyType = 2;
                    break;
                case "female":
                    FemalePage.Visibility = Visibility.Visible;
                    _currentVocabularyType = 3;
                    break;
                case "global":
                    GlobalPage.Visibility = Visibility.Visible;
                    _currentVocabularyType = 4;
                    break;
            }

            _ = LoadVocabularyDataAsync();
        }



        private void BtnAddMutual_Click(object sender, RoutedEventArgs e)
        {
            VocabularyEditWindow editWindow = new VocabularyEditWindow(_currentVocabularyType, null, _vocabularyService);
            editWindow.Owner = this;
            if (editWindow.ShowDialog() == true)
            {
                _ = LoadVocabularyDataAsync();
            }
        }

        private void BtnAddMale_Click(object sender, RoutedEventArgs e)
        {
            VocabularyEditWindow editWindow = new VocabularyEditWindow(2, null, _vocabularyService);
            editWindow.Owner = this;
            if (editWindow.ShowDialog() == true)
            {
                _ = LoadVocabularyDataAsync();
            }
        }

        private void BtnAddFemale_Click(object sender, RoutedEventArgs e)
        {
            VocabularyEditWindow editWindow = new VocabularyEditWindow(3, null, _vocabularyService);
            editWindow.Owner = this;
            if (editWindow.ShowDialog() == true)
            {
                _ = LoadVocabularyDataAsync();
            }
        }

        private void BtnAddGlobal_Click(object sender, RoutedEventArgs e)
        {
            VocabularyEditWindow editWindow = new VocabularyEditWindow(4, null, _vocabularyService);
            editWindow.Owner = this;
            if (editWindow.ShowDialog() == true)
            {
                _ = LoadVocabularyDataAsync();
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var dataContext = button?.DataContext as VocabularyDisplayItem;
            if (dataContext != null)
            {
                var vocabulary = new VocabularyInfo
                {
                    VocabularyId = dataContext.VocabularyId,
                    VocabularyType = dataContext.VocabularyType,
                    VocabularyName = dataContext.VocabularyName,
                    Remark = dataContext.Remark
                };

                VocabularyEditWindow editWindow = new VocabularyEditWindow(_currentVocabularyType, vocabulary, _vocabularyService);
                editWindow.Owner = this;
                if (editWindow.ShowDialog() == true)
                {
                    _ = LoadVocabularyDataAsync();
                }
            }
        }

        private async void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var dataContext = button?.DataContext as VocabularyDisplayItem;
            if (dataContext != null)
            {
                var result = MessageBox.Show("确定要删除这个词汇吗？此操作不可撤销！", "确认删除", 
                                           MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        bool success = await _vocabularyService.DeleteVocabularyAsync(dataContext.VocabularyId);
                        if (success)
                        {
                            MessageBox.Show("删除成功！", "提示", 
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadVocabularyDataAsync();
                        }
                        else
                        {
                            MessageBox.Show("删除失败！", "错误", 
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"删除失败：{ex.Message}", "错误", 
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            // 根据当前页面类型获取对应的搜索文本
            switch (_currentVocabularyType)
            {
                case 1: // 互斥词汇
                    _currentSearchText = txtMutualSearch.Text;
                    if (_currentSearchText == "搜索互斥词汇...")
                        _currentSearchText = "";
                    break;
                case 2: // 男禁用词
                    _currentSearchText = txtMaleSearch.Text;
                    if (_currentSearchText == "搜索男禁用词...")
                        _currentSearchText = "";
                    break;
                case 3: // 女禁用词
                    _currentSearchText = txtFemaleSearch.Text;
                    if (_currentSearchText == "搜索女禁用词...")
                        _currentSearchText = "";
                    break;
                case 4: // 全局禁用词
                    _currentSearchText = txtGlobalSearch.Text;
                    if (_currentSearchText == "搜索全局禁用词...")
                        _currentSearchText = "";
                    break;
            }
            
            await LoadVocabularyDataAsync();
        }

        private void SearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            string defaultText = GetDefaultSearchText(textBox);
            if (textBox.Text == defaultText)
            {
                textBox.Text = "";
                textBox.Foreground = new SolidColorBrush(Colors.Black);
            }
        }

        private void SearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                string defaultText = GetDefaultSearchText(textBox);
                textBox.Text = defaultText;
                textBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
            }
        }

        private string GetDefaultSearchText(TextBox textBox)
        {
            if (textBox == txtMutualSearch) return "搜索互斥词汇...";
            if (textBox == txtMaleSearch) return "搜索男禁用词...";
            if (textBox == txtFemaleSearch) return "搜索女禁用词...";
            if (textBox == txtGlobalSearch) return "搜索全局禁用词...";
            return "搜索...";
        }

        // 窗口控制事件
        private void BtnMinimize_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // 标题栏拖动事件
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ClickCount == 1)
            {
                this.DragMove();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _vocabularyService?.Dispose();
            base.OnClosed(e);
        }
    }

    // 用于DataGrid显示的数据模型
    public class VocabularyDisplayItem
    {
        public string 序号 { get; set; }
        public int VocabularyId { get; set; }
        public int VocabularyType { get; set; }
        public string VocabularyName { get; set; }
        public string 词汇A { get; set; }
        public string 词汇B { get; set; }
        public string 禁用词汇 { get; set; }
        public string Remark { get; set; }
        public string 操作时间 { get; set; }
        public string 操作人 { get; set; }
    }
}
